import SwiftUI

@MainActor
class ThemeManager: ObservableObject {
    @Published var isDarkMode: Bool = false
    @Published var themeMode: ThemeMode = .system {
        didSet {
            if oldValue != themeMode {
                updateTheme()
            }
        }
    }
    
    enum ThemeMode: String, CaseIterable, Identifiable {
        case light
        case dark
        case system
        
        var id: String { self.rawValue }
        
        var displayName: String {
            switch self {
            case .light: return "light_mode".localized
            case .dark: return "dark_mode".localized
            case .system: return "system_mode".localized
            }
        }
    }
    
    init() {
        updateTheme()
    }
    
    func updateTheme() {
        let newIsDarkMode: Bool
        switch themeMode {
        case .light:
            newIsDarkMode = false
        case .dark:
            newIsDarkMode = true
        case .system:
            newIsDarkMode = getCurrentSystemTheme()
        }
        
        if isDarkMode != newIsDarkMode {
            isDarkMode = newIsDarkMode
            applyTheme()
            NotificationCenter.default.post(name: .themeDidChange, object: nil)
        }
    }
    
    func getCurrentSystemTheme() -> Bool {
        return UITraitCollection.current.userInterfaceStyle == .dark
    }
    
    private func applyTheme() {
        updateWindows()
    }
    
    private func updateWindows() {
        let scenes = UIApplication.shared.connectedScenes
        let windowScenes = scenes.compactMap { $0 as? UIWindowScene }
        
        for windowScene in windowScenes {
            for window in windowScene.windows {
                window.overrideUserInterfaceStyle = themeMode == .system ? .unspecified : (isDarkMode ? .dark : .light)
            }
        }
    }
}

extension Notification.Name {
    static let themeDidChange = Notification.Name("themeDidChange")
}
