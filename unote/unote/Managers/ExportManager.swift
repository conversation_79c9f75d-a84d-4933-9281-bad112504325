import Foundation
import SwiftUI
import PDFKit
import UIKit

class ExportManager {
    static let shared = ExportManager()
    
    // MARK: - CSV Export
    func exportToCSV(items: [ChatItem]) -> URL? {
        // 扩展CSV字段,包含更多信息
        let headers = "日期,时间,类型,内容,金额,标签,完成状态,所属项目,优先级,计划日期,提醒时间,位置,备注\n"
        var csvString = headers
        
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd"
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm:ss"
        
        for item in items.sorted(by: { $0.effectiveTimestamp < $1.effectiveTimestamp }) {
            let row = [
                dateFormatter.string(from: item.effectiveTimestamp),
                timeFormatter.string(from: item.effectiveTimestamp),
                item.type.description,
                item.text.replacingOccurrences(of: ",", with: " "),
                item.amount?.description ?? "",
                item.tags.joined(separator: " "),
                item.completed ? "是" : "否",
                item.project?.name ?? "",
                item.priority.description,
                item.plannedDate.map { dateFormatter.string(from: $0) } ?? "",
                item.reminderTime.map { timeFormatter.string(from: $0) } ?? "",
                item.location ?? "",
                item.notes?.replacingOccurrences(of: ",", with: " ") ?? ""
            ].joined(separator: ",")
            
            csvString.append(row + "\n")
        }
        
        guard let data = csvString.data(using: .utf8) else { return nil }
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = "unote_export_\(Date().timeIntervalSince1970).csv"
        let fileURL = tempDir.appendingPathComponent(fileName)
        
        do {
            try data.write(to: fileURL)
            return fileURL
        } catch {
            print(String(format: "csv_export_error".localized, error.localizedDescription))
            return nil
        }
    }
    
    // MARK: - PDF Export
    func exportToPDF(items: [ChatItem]) -> URL? {
        let pageWidth: CGFloat = 595.2  // A4宽度
        let pageHeight: CGFloat = 841.8 // A4高度
        let margin: CGFloat = 50
        _ = pageWidth - (margin * 2)
        
        let pdfMetaData = [
            kCGPDFContextCreator: "uNote",
            kCGPDFContextAuthor: "uNote User",
            kCGPDFContextTitle: "我的uNote日记"
        ]
        
        let format = UIGraphicsPDFRendererFormat()
        format.documentInfo = pdfMetaData as [String: Any]
        
        let pageRect = CGRect(x: 0, y: 0, width: pageWidth, height: pageHeight)
        let renderer = UIGraphicsPDFRenderer(bounds: pageRect, format: format)
        
        // 按日期分组并排序
        let groupedItems = Dictionary(grouping: items) { item in
            Calendar.current.startOfDay(for: item.effectiveTimestamp)
        }.sorted { $0.key > $1.key }
        
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = "unote_diary_\(Date().timeIntervalSince1970).pdf"
        let fileURL = tempDir.appendingPathComponent(fileName)
        
        do {
            try renderer.writePDF(to: fileURL) { context in
                // 添加封面
                addCoverPage(context: context, pageRect: pageRect)
                
                // 添加目录
                addTableOfContents(context: context, pageRect: pageRect, dates: groupedItems.map { $0.key })
                
                // 绘制每一天的内容
                for (date, dayItems) in groupedItems {
                    context.beginPage()
                    var yPosition = drawDayHeader(date: date, pageRect: pageRect, margin: margin)
                    
                    // 绘制每日总结
                    yPosition = drawDailySummary(items: dayItems, yPosition: yPosition, pageRect: pageRect, margin: margin)
                    
                    // 按类型分组内容
                    let typeGroups = Dictionary(grouping: dayItems) { $0.type }
                    
                    // 绘制不同类型的内容
                    for type in [ItemType.note, .task, .expense] {
                        if let items = typeGroups[type] {
                            yPosition = drawTypeSection(
                                type: type,
                                items: items.sorted { $0.effectiveTimestamp < $1.effectiveTimestamp },
                                yPosition: yPosition,
                                pageRect: pageRect,
                                margin: margin,
                                context: context
                            )
                        }
                    }
                }
            }
            return fileURL
        } catch {
            print(String(format: "pdf_export_error".localized, error.localizedDescription))
            return nil
        }
    }
    
    // MARK: - Private PDF Helper Methods
    private func addCoverPage(context: UIGraphicsPDFRendererContext, pageRect: CGRect) {
        context.beginPage()
        
        let titleFont = UIFont.systemFont(ofSize: 36, weight: .bold)
        let subtitleFont = UIFont.systemFont(ofSize: 18)
        let dateFont = UIFont.systemFont(ofSize: 14)
        
        let title = "pdf_diary_title".localized
        let subtitle = "pdf_diary_subtitle".localized
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "year_month_day_format".localized
        let dateString = "pdf_export_date".localized + dateFormatter.string(from: Date())
        
        // 绘制标题
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: titleFont,
            .foregroundColor: UIColor.black
        ]
        let titleSize = (title as NSString).size(withAttributes: titleAttributes)
        let titleX = (pageRect.width - titleSize.width) / 2
        let titleY = pageRect.height / 3
        title.draw(at: CGPoint(x: titleX, y: titleY), withAttributes: titleAttributes)
        
        // 绘制副标题
        let subtitleAttributes: [NSAttributedString.Key: Any] = [
            .font: subtitleFont,
            .foregroundColor: UIColor.gray
        ]
        let subtitleSize = (subtitle as NSString).size(withAttributes: subtitleAttributes)
        let subtitleX = (pageRect.width - subtitleSize.width) / 2
        subtitle.draw(at: CGPoint(x: subtitleX, y: titleY + 60), withAttributes: subtitleAttributes)
        
        // 绘制日期
        let dateAttributes: [NSAttributedString.Key: Any] = [
            .font: dateFont,
            .foregroundColor: UIColor.gray
        ]
        let dateSize = (dateString as NSString).size(withAttributes: dateAttributes)
        let dateX = (pageRect.width - dateSize.width) / 2
        dateString.draw(at: CGPoint(x: dateX, y: pageRect.height - 100), withAttributes: dateAttributes)
    }
    
    private func addTableOfContents(context: UIGraphicsPDFRendererContext, pageRect: CGRect, dates: [Date]) {
        context.beginPage()
        
        let titleFont = UIFont.systemFont(ofSize: 24, weight: .bold)
        let contentFont = UIFont.systemFont(ofSize: 14)
        let margin: CGFloat = 50
        var yPosition: CGFloat = margin
        
        // 绘制目录标题
        let title = "pdf_table_of_contents".localized
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: titleFont,
            .foregroundColor: UIColor.black
        ]
        title.draw(at: CGPoint(x: margin, y: yPosition), withAttributes: titleAttributes)
        yPosition += 40
        
        // 绘制日期列表
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "year_month_day_format".localized
        
        let contentAttributes: [NSAttributedString.Key: Any] = [
            .font: contentFont,
            .foregroundColor: UIColor.black
        ]
        
        for date in dates {
            let dateString = dateFormatter.string(from: date)
            dateString.draw(at: CGPoint(x: margin, y: yPosition), withAttributes: contentAttributes)
            yPosition += 25
            
            if yPosition > pageRect.height - margin {
                context.beginPage()
                yPosition = margin
            }
        }
    }
    
    private func drawDayHeader(date: Date, pageRect: CGRect, margin: CGFloat) -> CGFloat {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "year_month_day_format".localized
        let weekdayFormatter = DateFormatter()
        weekdayFormatter.dateFormat = "weekday_format".localized
        
        let dateString = dateFormatter.string(from: date)
        let weekdayString = weekdayFormatter.string(from: date)
        
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 24, weight: .bold),
            .foregroundColor: UIColor.black
        ]
        
        let subtitleAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.gray
        ]
        
        dateString.draw(at: CGPoint(x: margin, y: margin), withAttributes: titleAttributes)
        weekdayString.draw(at: CGPoint(x: margin, y: margin + 30), withAttributes: subtitleAttributes)
        
        // 绘制分隔线
        let path = UIBezierPath()
        path.move(to: CGPoint(x: margin, y: margin + 60))
        path.addLine(to: CGPoint(x: pageRect.width - margin, y: margin + 60))
        UIColor.gray.withAlphaComponent(0.3).setStroke()
        path.stroke()
        
        return margin + 80
    }
    
    private func drawDailySummary(items: [ChatItem], yPosition: CGFloat, pageRect: CGRect, margin: CGFloat) -> CGFloat {
        let summaryAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor.gray
        ]
        
        // 生成每日总结文本
        let summary = generateDailySummary(items: items)
        let summaryRect = CGRect(x: margin, y: yPosition, width: pageRect.width - (margin * 2), height: 100)
        
        summary.draw(in: summaryRect, withAttributes: summaryAttributes)
        
        return yPosition + 80
    }
    
    private func drawTypeSection(type: ItemType, items: [ChatItem], yPosition: CGFloat, pageRect: CGRect, margin: CGFloat, context: UIGraphicsPDFRendererContext) -> CGFloat {
        var currentY = yPosition
        
        // 绘制类型标题
        let titleAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 18, weight: .semibold),
            .foregroundColor: UIColor.black
        ]
        
        type.description.draw(at: CGPoint(x: margin, y: currentY), withAttributes: titleAttributes)
        currentY += 30
        
        // 绘制内容
        let contentAttributes: [NSAttributedString.Key: Any] = [
            .font: UIFont.systemFont(ofSize: 14),
            .foregroundColor: UIColor.black
        ]
        
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "HH:mm"
        
        for item in items {
            // 检查是否需要新页
            if currentY > pageRect.height - margin {
                context.beginPage()
                currentY = margin
            }
            
            let timeString = timeFormatter.string(from: item.effectiveTimestamp)
            let content = "\(timeString) \(item.text)"
            
            // 计算文本高度
            let contentRect = CGRect(x: margin, y: currentY, width: pageRect.width - (margin * 2), height: 1000)
            let textHeight = (content as NSString).boundingRect(
                with: CGSize(width: contentRect.width, height: .greatestFiniteMagnitude),
                options: [.usesFontLeading, .usesLineFragmentOrigin],
                attributes: contentAttributes,
                context: nil
            ).height
            
            content.draw(in: CGRect(x: margin, y: currentY, width: pageRect.width - (margin * 2), height: textHeight),
                        withAttributes: contentAttributes)
            
            currentY += textHeight + 10
            
            // 如果有图片，绘制图片
            if !item.imageData.isEmpty {
                currentY = drawImages(item.imageData, startY: currentY, pageRect: pageRect, margin: margin, context: context)
            }
        }
        
        return currentY + 20
    }
    
    private func drawImages(_ imageDataArray: [Data], startY: CGFloat, pageRect: CGRect, margin: CGFloat, context: UIGraphicsPDFRendererContext) -> CGFloat {
        var currentY = startY + 5 // 减少顶部间距
        let contentWidth = pageRect.width - (margin * 2)
        let maxImageWidth: CGFloat = contentWidth * 0.7 // 减小图片最大宽度为70%
        let maxImageHeight: CGFloat = 200 // 减小最大高度
        let imageSpacing: CGFloat = 8 // 减少图片间距
        let imagesPerRow = 2 // 每行显示2张图片
        
        // 优化内存使用的autoreleasepool
        autoreleasepool {
            // 将图片分组为每行两张
            for i in stride(from: 0, to: imageDataArray.count, by: imagesPerRow) {
                let rowImages = imageDataArray[i..<min(i + imagesPerRow, imageDataArray.count)]
                let rowImageWidth = maxImageWidth / CGFloat(imagesPerRow) * 0.95 // 留一点间距
                
                var maxRowHeight: CGFloat = 0
                var processedImages: [(UIImage, CGSize)] = []
                
                // 第一遍循环计算这一行所有图片的尺寸
                for imageData in rowImages {
                    guard let image = UIImage(data: imageData)?.optimizedForPDFExport() else { continue }
                    
                    let size = calculateOptimalImageSize(
                        image: image,
                        maxWidth: rowImageWidth,
                        maxHeight: maxImageHeight
                    )
                    
                    processedImages.append((image, size))
                    maxRowHeight = max(maxRowHeight, size.height)
                }
                
                // 检查是否需要新页
                if currentY + maxRowHeight > pageRect.height - margin {
                    context.beginPage()
                    currentY = margin + 5
                }
                
                // 第二遍循环绘制图片
                var currentX = margin
                for (image, size) in processedImages {
                    // 居中显示图片
                    let imageY = currentY + (maxRowHeight - size.height) / 2
                    
                    // 简化的边框效果（只保留细线框）
                    let borderRect = CGRect(
                        x: currentX,
                        y: imageY,
                        width: size.width,
                        height: size.height
                    )
                    
                    // 绘制简单边框
                    let borderPath = UIBezierPath(rect: borderRect)
                    UIColor.gray.withAlphaComponent(0.3).setStroke()
                    borderPath.lineWidth = 0.5
                    borderPath.stroke()
                    
                    // 绘制图片
                    image.draw(in: borderRect)
                    
                    currentX += size.width + (contentWidth - maxImageWidth) / CGFloat(imagesPerRow - 1)
                }
                
                currentY += maxRowHeight + imageSpacing
            }
        }
        
        return currentY
    }
    
    private func calculateOptimalImageSize(image: UIImage, maxWidth: CGFloat, maxHeight: CGFloat) -> CGSize {
        let aspectRatio = image.size.width / image.size.height
        
        // 优先考虑宽度限制
        var width = min(maxWidth, image.size.width)
        var height = width / aspectRatio
        
        // 如果高度超出限制，则按高度计算
        if height > maxHeight {
            height = maxHeight
            width = height * aspectRatio
        }
        
        // 确保尺寸不会太小
        let minDimension: CGFloat = 60
        if width < minDimension || height < minDimension {
            if aspectRatio > 1 {
                width = minDimension * aspectRatio
                height = minDimension
            } else {
                width = minDimension
                height = minDimension / aspectRatio
            }
        }
        
        return CGSize(width: width, height: height)
    }
    
    private func generateDailySummary(items: [ChatItem]) -> String {
        var summary = ""
        
        // 任务统计
        let tasks = items.filter { $0.type == .task }
        if !tasks.isEmpty {
            let completed = tasks.filter { $0.completed }.count
            summary += "今日完成了\(completed)/\(tasks.count)项任务。"
        }
        
        // 笔记统计
        let notes = items.filter { $0.type == .note }
        if !notes.isEmpty {
            let totalWords = notes.reduce(0) { $0 + $1.text.count }
            summary += "记录了\(notes.count)篇笔记，共\(totalWords)字。"
        }
        
        // 账单统计
        let expenses = items.filter { $0.type == .expense }
        if !expenses.isEmpty {
            let income = expenses.filter { $0.amount ?? 0 > 0 }.reduce(0) { $0 + ($1.amount ?? 0) }
            let expenditure = abs(expenses.filter { $0.amount ?? 0 < 0 }.reduce(0) { $0 + ($1.amount ?? 0) })
            
            if income > 0 {
                summary += "收入¥\(String(format: "%.2f", income))，"
            }
            if expenditure > 0 {
                summary += "支出¥\(String(format: "%.2f", expenditure))，"
            }
            if income > expenditure {
                summary += "结余¥\(String(format: "%.2f", income - expenditure))。"
            }
        }
        
        return summary
    }
}

private extension UIImage {
    func optimizedForPDFExport() -> UIImage? {
        // 如果图片太大，进行更激进的压缩
        let maxDimension: CGFloat = 1024 // 降低最大尺寸
        let scale: CGFloat
        
        if size.width > maxDimension || size.height > maxDimension {
            scale = maxDimension / max(size.width, size.height)
        } else {
            scale = 1.0
        }
        
        if scale < 1.0 {
            let newSize = CGSize(
                width: size.width * scale,
                height: size.height * scale
            )
            
            UIGraphicsBeginImageContextWithOptions(newSize, false, 0.8) // 降低图片质量
            defer { UIGraphicsEndImageContext() }
            
            draw(in: CGRect(origin: .zero, size: newSize))
            return UIGraphicsGetImageFromCurrentImageContext()
        }
        
        return self
    }
} 
