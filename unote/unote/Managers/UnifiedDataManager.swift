import SwiftUI
import SwiftData
import Foundation

// MARK: - 统一数据管理器
@MainActor
final class UnifiedDataManager: ObservableObject {
    static let shared = UnifiedDataManager()
    
    private var modelContext: ModelContext?
    
    private init() {}
    
    // MARK: - Setup
    func setup(modelContext: ModelContext) {
        self.modelContext = modelContext
    }
    
    // MARK: - Project Operations
    func createProject(name: String, avatarType: AvatarType = .icon, avatarName: String = "folder") -> Project {
        let project = Project(name: name, avatarType: avatarType, avatarName: avatarName)
        modelContext?.insert(project)
        saveContext()
        return project
    }
    
    func updateProject(_ project: Project, name: String? = nil, avatarName: String? = nil) {
        if let name = name {
            project.name = name
        }
        if let avatarName = avatarName {
            project.avatarName = avatarName
        }
        project.updateLastUpdated()
        saveContext()
    }
    
    func deleteProject(_ project: Project) {
        modelContext?.delete(project)
        saveContext()
    }
    
    func fetchProjects() -> [Project] {
        guard let context = modelContext else { return [] }
        let descriptor = FetchDescriptor<Project>(sortBy: [SortDescriptor(\.lastUpdated, order: .reverse)])
        return (try? context.fetch(descriptor)) ?? []
    }
    
    // MARK: - ChatItem Operations
    func createChatItem(
        text: String,
        type: ItemType = .note,
        amount: Double? = nil,
        project: Project? = nil,
        tags: [String] = [],
        pomodoroCount: Int? = nil,
        imageData: [Data] = []
    ) -> ChatItem {
        let item = ChatItem(
            text: text,
            amount: amount,
            type: type,
            tags: tags,
            pomodoroCount: pomodoroCount,
            imageData: imageData,
            project: project
        )
        
        modelContext?.insert(item)
        project?.updateLastUpdated()
        saveContext()
        return item
    }
    
    func updateChatItem(_ item: ChatItem, text: String? = nil, completed: Bool? = nil) {
        if let text = text {
            item.text = text
        }
        if let completed = completed {
            item.completed = completed
        }
        item.project?.updateLastUpdated()
        saveContext()
    }
    
    func deleteChatItem(_ item: ChatItem) {
        item.project?.updateLastUpdated()
        modelContext?.delete(item)
        saveContext()
    }
    
    func fetchChatItems(for project: Project? = nil, filter: ContentFilter = .all) -> [ChatItem] {
        guard let context = modelContext else { return [] }
        
        let descriptor = FetchDescriptor<ChatItem>(
            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
        )
        
        let items = (try? context.fetch(descriptor)) ?? []
        
        // 过滤项目
        let projectFiltered = if let project = project {
            items.filter { $0.project?.id == project.id }
        } else {
            items
        }
        
        // 应用内容过滤器
        return projectFiltered.filter { filter.matches(item: $0, project: project) }
    }
    
    // MARK: - 优化的搜索操作
    private var searchCache: [String: [ChatItem]] = [:]
    private var lastSearchTime: Date = Date()
    
    func searchItems(query: String) -> [ChatItem] {
        guard let context = modelContext else { return [] }
        let trimmedQuery = query.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedQuery.isEmpty else { return [] }
        
        // 检查缓存
        if let cachedResult = searchCache[trimmedQuery],
           Date().timeIntervalSince(lastSearchTime) < 30 {
            return cachedResult
        }
        
        // 优化的数据库查询
        let predicate = #Predicate<ChatItem> { item in
            item.text.localizedStandardContains(trimmedQuery)
        }
        
        let descriptor = FetchDescriptor<ChatItem>(
            predicate: predicate,
            sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
        )
        
        let items = (try? context.fetch(descriptor)) ?? []
        
        // 在内存中额外搜索标签，但限制结果数量
        let filteredItems = items.filter { item in
            item.text.localizedStandardContains(trimmedQuery) ||
            item.tags.contains { tag in
                tag.localizedStandardContains(trimmedQuery)
            }
        }.prefix(100) // 限制结果数量优化性能
        
        let result = Array(filteredItems)
        
        // 缓存结果
        searchCache[trimmedQuery] = result
        lastSearchTime = Date()
        
        // 清理过旧缓存
        if searchCache.count > 20 {
            let sortedKeys = searchCache.keys.sorted()
            for key in sortedKeys.prefix(searchCache.count - 15) {
                searchCache.removeValue(forKey: key)
            }
        }
        
        return result
    }
    
    // MARK: - 优化的统计操作
    private var statisticsCache: [String: AppStatistics] = [:]
    private var statisticsCacheTime: [String: Date] = [:]
    
    func getStatistics(for project: Project? = nil, dateRange: DateRange = .thisMonth) -> AppStatistics {
        let cacheKey = "\(project?.id.uuidString ?? "all")_\(dateRange)"
        
        // 检查缓存（5分钟内有效）
        if let cachedStats = statisticsCache[cacheKey],
           let cacheTime = statisticsCacheTime[cacheKey],
           Date().timeIntervalSince(cacheTime) < 300 {
            return cachedStats
        }
        
        let items = fetchChatItems(for: project)
        let filteredItems = items.filter { dateRange.contains($0.timestamp) }
        
        // 优化：使用单次遍历计算所有统计
        var totalItems = 0
        var completedTasks = 0
        var totalTasks = 0
        var totalExpenses: Double = 0
        var totalPomodoros = 0
        
        for item in filteredItems {
            totalItems += 1
            
            if item.type == .task {
                totalTasks += 1
                if item.completed {
                    completedTasks += 1
                }
            }
            
            if item.type == .expense, let amount = item.amount {
                totalExpenses += amount
            }
            
            if let pomodoroCount = item.pomodoroCount {
                totalPomodoros += pomodoroCount
            }
        }
        
        let statistics = AppStatistics(
            totalItems: totalItems,
            completedTasks: completedTasks,
            totalTasks: totalTasks,
            totalExpenses: totalExpenses,
            totalPomodoros: totalPomodoros,
            efficiency: totalTasks > 0 ? Double(completedTasks) / Double(totalTasks) : 0
        )
        
        // 缓存结果
        statisticsCache[cacheKey] = statistics
        statisticsCacheTime[cacheKey] = Date()
        
        return statistics
    }
    
    // MARK: - 优化的标签操作
    private var tagsCache: [String] = []
    private var tagsCacheTime: Date?
    
    func getAllTags() -> [String] {
        // 检查缓存（10分钟内有效）
        if let cacheTime = tagsCacheTime,
           Date().timeIntervalSince(cacheTime) < 600,
           !tagsCache.isEmpty {
            return tagsCache
        }
        
        let items = fetchChatItems()
        let allTags = items.flatMap { $0.tags }
        let uniqueTags = Array(Set(allTags)).sorted()
        
        // 缓存结果
        tagsCache = uniqueTags
        tagsCacheTime = Date()
        
        return uniqueTags
    }
    
    // 清理缓存方法
    func invalidateCache() {
        searchCache.removeAll()
        statisticsCache.removeAll()
        statisticsCacheTime.removeAll()
        tagsCache.removeAll()
        tagsCacheTime = nil
    }
    
    // MARK: - Helper Methods
    private func saveContext() {
        do {
            try modelContext?.save()
            // 数据变更后清理相关缓存
            invalidateCache()
        } catch {
            print(String(format: "save_data_failed".localized, error.localizedDescription))
        }
    }
}

// MARK: - Statistics Model
struct AppStatistics {
    let totalItems: Int
    let completedTasks: Int
    let totalTasks: Int
    let totalExpenses: Double
    let totalPomodoros: Int
    let efficiency: Double
    
    var completionRate: String {
        String(format: "%.1f%%", efficiency * 100)
    }
}

// MARK: - Date Range Enum
enum DateRange {
    case today
    case thisWeek
    case thisMonth
    case thisYear
    case custom(from: Date, to: Date)
    
    var title: String {
        switch self {
        case .today: return "date_range_today".localized
        case .thisWeek: return "date_range_this_week".localized
        case .thisMonth: return "date_range_this_month".localized
        case .thisYear: return "date_range_this_year".localized
        case .custom: return "date_range_custom".localized
        }
    }
    
    func contains(_ date: Date) -> Bool {
        let calendar = Calendar.current
        let now = Date()
        
        switch self {
        case .today:
            return calendar.isDate(date, inSameDayAs: now)
        case .thisWeek:
            return calendar.isDate(date, equalTo: now, toGranularity: .weekOfYear)
        case .thisMonth:
            return calendar.isDate(date, equalTo: now, toGranularity: .month)
        case .thisYear:
            return calendar.isDate(date, equalTo: now, toGranularity: .year)
        case .custom(let from, let to):
            return date >= from && date <= to
        }
    }
} 