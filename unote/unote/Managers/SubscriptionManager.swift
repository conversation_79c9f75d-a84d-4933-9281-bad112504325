import Foundation
import StoreKit
import UserNotifications

// MARK: - 订阅状态通知
extension Notification.Name {
    static let subscriptionStatusChanged = Notification.Name("com.unote.subscriptionStatusChanged")
}

// MARK: - 订阅错误枚举
enum SubscriptionError: Error {
    case verificationFailed
    case noValidSubscriptionFound
    case userCancelled
    case purchasePending
    case unknown
    
    var localizedDescription: String {
        switch self {
        case .verificationFailed:
            return "purchase_verification_failed".localized
        case .noValidSubscriptionFound:
            return "subscription_not_found".localized
        case .userCancelled:
            return "purchase_cancelled".localized
        case .purchasePending:
            return "purchase_processing".localized
        case .unknown:
            return "unknown_purchase_error".localized
        }
    }
}

// MARK: - 订阅管理器
@MainActor
class SubscriptionManager: ObservableObject {
    static let shared = SubscriptionManager()
    
    @Published var isSubscribed = false
    @Published var purchaseSuccess = false
    
    // 防止重复验证
    private var isSyncing = false
    private var lastSyncTime: Date?
    private let syncCooldown: TimeInterval = 30 // 30秒冷却时间
    
    // 简化的产品类型
    enum ProductType: String {
        case monthly = "sub_pro"
        case yearly = "sub_pro_year" 
        case lifetime = "pro_lifetime"
    }
    
    private init() {
        // 读取本地状态
        loadSubscriptionStatus()
        
        // 异步同步App Store状态（仅在需要时）
        Task {
            await syncSubscriptionStatusIfNeeded()
        }
    }
    
    // MARK: - Public Methods
    
    /// 同步App Store订阅状态（带防重复机制）
    func syncSubscriptionStatus() async {
        await syncSubscriptionStatusIfNeeded(force: true)
    }
    
    /// 智能同步订阅状态（公开方法，供外部调用）
    func syncSubscriptionStatusIfNeeded() async {
        // 如果已经订阅且不是即将过期，跳过验证
        if isSubscribed {
            if UserDefaults.standard.bool(forKey: "isLifetime") {
                print("永久会员，跳过验证")
                return
            }
            
            if let expiryDate = UserDefaults.standard.object(forKey: "expiryDate") as? Date,
               expiryDate > Date() {
                let daysUntilExpiry = Calendar.current.dateComponents([.day], from: Date(), to: expiryDate).day ?? 0
                if daysUntilExpiry > 7 {
                    print("订阅有效且未即将过期，跳过验证")
                    return
                }
            }
        }
        
        await syncSubscriptionStatusIfNeeded(force: false)
    }
    
    /// 智能同步订阅状态（仅在需要时）
    private func syncSubscriptionStatusIfNeeded(force: Bool = false) async {
        // 检查是否正在同步
        if isSyncing && !force {
            print("订阅同步已在进行中，跳过")
            return
        }
        
        // 检查冷却时间
        if let lastSync = lastSyncTime,
           Date().timeIntervalSince(lastSync) < syncCooldown && !force {
            print("订阅同步冷却中，跳过")
            return
        }
        
        isSyncing = true
        lastSyncTime = Date()
        
        do {
            try await AppStore.sync()
            
            for await result in Transaction.currentEntitlements {
                if case .verified(let transaction) = result {
                    await MainActor.run {
                        if transaction.productID == ProductType.lifetime.rawValue {
                            // 永久会员
                            UserDefaults.standard.set(true, forKey: "isLifetime")
                            self.isSubscribed = true
                        } else if let expiryDate = transaction.expirationDate,
                                  expiryDate > Date() {
                            // 有效订阅
                            UserDefaults.standard.set(expiryDate, forKey: "expiryDate")
                            UserDefaults.standard.set(transaction.productID, forKey: "subscriptionType")
                            self.isSubscribed = true
                        } else {
                            // 订阅已过期
                            self.isSubscribed = false
                            UserDefaults.standard.removeObject(forKey: "expiryDate")
                            UserDefaults.standard.removeObject(forKey: "subscriptionType")
                        }
                    }
                    break
                }
            }
            
            // 没有找到有效订阅
            await MainActor.run {
                self.isSubscribed = false
            }
            
        } catch {
            print("订阅状态同步失败: \(error.localizedDescription)")
        }
        
        isSyncing = false
    }
    
    private func loadSubscriptionStatus() {
        // 检查永久会员
        if UserDefaults.standard.bool(forKey: "isLifetime") {
            self.isSubscribed = true
            print("检测到永久会员，跳过验证")
            return
        }
        
        // 检查订阅到期时间
        if let expiryDate = UserDefaults.standard.object(forKey: "expiryDate") as? Date {
            if expiryDate > Date() {
                self.isSubscribed = true
                print("检测到有效订阅，到期时间: \(expiryDate)")
                
                // 只有在订阅即将过期时才进行验证（提前7天）
                let daysUntilExpiry = Calendar.current.dateComponents([.day], from: Date(), to: expiryDate).day ?? 0
                if daysUntilExpiry <= 7 {
                    print("订阅即将过期，进行验证")
                    Task {
                        await syncSubscriptionStatusIfNeeded(force: true)
                    }
                }
                return
            } else {
                // 订阅已过期，清理本地数据
                UserDefaults.standard.removeObject(forKey: "expiryDate")
                UserDefaults.standard.removeObject(forKey: "subscriptionType")
                self.isSubscribed = false
                print("订阅已过期，清理本地数据")
            }
        }
        
        // 检查是否需要验证
        if needsValidation() {
            print("需要验证订阅状态")
            UserDefaults.standard.set(Date(), forKey: "lastValidationTime")
            // 异步验证，不阻塞UI
            Task {
                await syncSubscriptionStatusIfNeeded(force: true)
            }
        } else {
            print("无需验证订阅状态")
        }
    }
    
    func purchase(_ product: Product) async throws {
        let result = try await product.purchase()
        
        switch result {
        case .success(let verification):
            switch verification {
            case .verified(let transaction):
                // 完成交易
                await transaction.finish()
                
                // 更新状态
                await MainActor.run {
                    if transaction.productID == ProductType.lifetime.rawValue {
                        UserDefaults.standard.set(true, forKey: "isLifetime")
                    } else if let expiryDate = transaction.expirationDate {
                        UserDefaults.standard.set(expiryDate, forKey: "expiryDate")
                        UserDefaults.standard.set(transaction.productID, forKey: "subscriptionType")
                    }
                    
                    self.isSubscribed = true
                    self.purchaseSuccess = true
                    
                    NotificationCenter.default.post(
                        name: .subscriptionStatusChanged,
                        object: nil
                    )
                }
                
            case .unverified(_, _):
                throw SubscriptionError.verificationFailed
            }
            
        case .pending:
            throw SubscriptionError.purchasePending
        case .userCancelled:
            throw SubscriptionError.userCancelled
        @unknown default:
            throw SubscriptionError.unknown
        }
    }
    
    func restorePurchases() async throws {
        await syncSubscriptionStatus()
        
        if !isSubscribed {
            throw SubscriptionError.noValidSubscriptionFound
        }
    }
    
    func canCreateNewProject(currentProjectCount: Int) -> Bool {
        // 如果已订阅，可以创建无限个项目
        if isSubscribed {
            return true
        }
        // 免费版限制3个项目
        return currentProjectCount < 3
    }
    
    /// 检查是否需要验证订阅状态
    private func needsValidation() -> Bool {
        // 永久会员不需要验证
        if UserDefaults.standard.bool(forKey: "isLifetime") {
            return false
        }
        
        // 如果已订阅且未即将过期，不需要验证
        if isSubscribed {
            if let expiryDate = UserDefaults.standard.object(forKey: "expiryDate") as? Date,
               expiryDate > Date() {
                let daysUntilExpiry = Calendar.current.dateComponents([.day], from: Date(), to: expiryDate).day ?? 0
                return daysUntilExpiry <= 7 // 只有即将过期才验证
            }
        }
        
        // 未订阅用户，检查上次验证时间
        let lastValidation = UserDefaults.standard.object(forKey: "lastValidationTime") as? Date ?? Date.distantPast
        return Date().timeIntervalSince(lastValidation) > 86400 // 24小时
    }
    
    func getSubscriptionStatusText() -> String {
        if UserDefaults.standard.bool(forKey: "isLifetime") {
            return "lifetime_member_status".localized
        } else if let expiryDate = UserDefaults.standard.object(forKey: "expiryDate") as? Date {
            let formatter = DateFormatter()
            formatter.dateStyle = .medium
            return String(format: "subscription_expires_on".localized, formatter.string(from: expiryDate))
        }
        return "free_member_status".localized
    }
    
    /// 调试方法：打印当前订阅状态
    func debugSubscriptionStatus() {
        print("=== 订阅状态调试 ===")
        print("isSubscribed: \(isSubscribed)")
        print("isSyncing: \(isSyncing)")
        print("isLifetime: \(UserDefaults.standard.bool(forKey: "isLifetime"))")
        if let expiryDate = UserDefaults.standard.object(forKey: "expiryDate") as? Date {
            print("expiryDate: \(expiryDate)")
            print("isExpired: \(expiryDate <= Date())")
        } else {
            print("expiryDate: nil")
        }
        if let subscriptionType = UserDefaults.standard.string(forKey: "subscriptionType") {
            print("subscriptionType: \(subscriptionType)")
        } else {
            print("subscriptionType: nil")
        }
        if let lastSync = lastSyncTime {
            print("lastSyncTime: \(lastSync)")
            print("timeSinceLastSync: \(Date().timeIntervalSince(lastSync))s")
        } else {
            print("lastSyncTime: nil")
        }
        if let lastValidation = UserDefaults.standard.object(forKey: "lastValidationTime") as? Date {
            print("lastValidationTime: \(lastValidation)")
        } else {
            print("lastValidationTime: nil")
        }
        print("==================")
    }
    
    /// 调试方法：检查项目编辑权限
    func debugProjectEditability(project: Project?) {
        print("=== 项目编辑权限调试 ===")
        if let project = project {
            print("项目名称: \(project.name)")
            print("项目ID: \(project.id)")
            print("最后更新: \(project.lastUpdated)")
            let isEditable = project.isEditable(isSubscribed: isSubscribed)
            print("项目可编辑: \(isEditable)")
            print("订阅状态: \(isSubscribed)")
        } else {
            print("项目: nil")
        }
        print("=====================")
    }
} 