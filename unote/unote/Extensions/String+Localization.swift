import Foundation
import SwiftUI

// MARK: - 本地化扩展
extension String {
    /// 获取本地化字符串 - 支持动态语言切换
    var localized: String {
        return LocalizationManager.shared.localizedString(for: self)
    }
    
    /// 带参数的本地化字符串
    func localized(_ arguments: CVarArg...) -> String {
        String(format: self.localized, arguments: arguments)
    }
}

// MARK: - 快捷访问本地化字符串
enum L10n {
    // 通用
    static let appName = "app_name".localized
    static let save = "save".localized
    static let cancel = "cancel".localized
    static let delete = "delete".localized
    static let edit = "edit".localized
    static let done = "done".localized
    static let loading = "loading".localized
    static let error = "error".localized
    static let success = "success".localized
    
    // 导航
    static let home = "home".localized
    static let settings = "settings".localized
    static let search = "search".localized
    
    // 项目和内容
    static let inputPlaceholder = "input_placeholder".localized
    static let sendButton = "send_button".localized
    static let noteType = "note_type".localized
    static let taskType = "task_type".localized
    static let expenseType = "expense_type".localized
    static let pomodoroLabel = "pomodoro_label".localized
    
    // 项目管理
    static let project = "project".localized
    static let projects = "projects".localized
    static let createProject = "create_project".localized
    static let editProject = "edit_project".localized
    static let deleteProject = "delete_project".localized
    static let projectName = "project_name".localized
    
    // 状态
    static let completed = "completed".localized
    static let pending = "pending".localized
    static let inProgress = "in_progress".localized
    
    // 统计
    static let statistics = "statistics".localized
    static let dailyStats = "daily_stats".localized
    static let weeklyStats = "weekly_stats".localized
    static let monthlyStats = "monthly_stats".localized
    static let efficiency = "efficiency".localized
    
    // 设置
    static let language = "language".localized
    static let theme = "theme".localized
    static let darkMode = "dark_mode".localized
    static let lightMode = "light_mode".localized
    static let systemMode = "system_mode".localized
    
    // 日期
    static let today = "today".localized
    static let yesterday = "yesterday".localized
    static let tomorrow = "tomorrow".localized
    static let thisWeek = "this_week".localized
    static let thisMonth = "this_month".localized
    
    // 标签
    static let tags = "tags".localized
    static let addTag = "add_tag".localized
    static let removeTag = "remove_tag".localized
    
    // 错误信息
    static let networkError = "network_error".localized
    static let dataError = "data_error".localized
    static let unknownError = "unknown_error".localized
}

// MARK: - SwiftUI 本地化视图
struct LocalizedText: View {
    private let key: String
    
    init(_ key: String) {
        self.key = key
    }
    
    var body: some View {
        Text(key.localized)
    }
}

// 支持 LocalizedStringKey 的版本
struct TypedLocalizedText: View {
    private let key: LocalizedStringKey
    
    init(_ key: LocalizedStringKey) {
        self.key = key
    }
    
    var body: some View {
        Text(key.localizedString)
    }
}

// MARK: - 本地化修饰符
extension Text {
    init(localized key: String) {
        self.init(key.localized)
    }
    
    init(localized key: LocalizedStringKey) {
        self.init(key.localizedString)
    }
}

extension View {
    func localizedTitle(_ key: String) -> some View {
        self.navigationTitle(key.localized)
    }
    
    func localizedTitle(_ key: LocalizedStringKey) -> some View {
        self.navigationTitle(key.localizedString)
    }
} 