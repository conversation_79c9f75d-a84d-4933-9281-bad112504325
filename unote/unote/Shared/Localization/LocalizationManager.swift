import Foundation
import SwiftUI

// MARK: - String Keys for Type Safety
enum LocalizedStringKey: String, CaseIterable {
    // Common
    case appName = "app_name"
    case save = "save"
    case cancel = "cancel"
    case delete = "delete"
    case edit = "edit"
    case done = "done"
    case confirm = "confirm"
    case back = "back"
    case next = "next"
    case previous = "previous"
    case retry = "retry"
    case loading = "loading"
    case error = "error"
    case success = "success"
    case warning = "warning"
    case info = "info"
    
    // Navigation
    case home = "home"
    case settings = "settings"
    case search = "search"
    case profile = "profile"
    case notifications = "notifications"
    
    // Chat & Items
    case inputPlaceholder = "input_placeholder"
    case sendButton = "send_button"
    case noteType = "note_type"
    case taskType = "task_type"
    case expenseType = "expense_type"
    case pomodoroLabel = "pomodoro_label"
    case addNote = "add_note"
    case addTask = "add_task"
    case addExpense = "add_expense"
    case completed = "completed"
    case pending = "pending"
    case inProgress = "in_progress"
    
    // Projects
    case project = "project"
    case projects = "projects"
    case createProject = "create_project"
    case editProject = "edit_project"
    case deleteProject = "delete_project"
    case projectName = "project_name"
    case projectDescription = "project_description"
    case selectProject = "select_project"
    
    // Statistics
    case statistics = "statistics"
    case dailyStats = "daily_stats"
    case weeklyStats = "weekly_stats"
    case monthlyStats = "monthly_stats"
    case totalTasks = "total_tasks"
    case completedTasks = "completed_tasks"
    case efficiency = "efficiency"
    case pomodoroCount = "pomodoro_count"
    
    // Settings
    case language = "language"
    case theme = "theme"
    case notificationsSettings = "notifications_settings"
    case export = "export"
    case importData = "import_data"
    case backup = "backup"
    case restore = "restore"
    case darkMode = "dark_mode"
    case lightMode = "light_mode"
    case systemMode = "system_mode"
    
    // Errors
    case networkError = "network_error"
    case dataError = "data_error"
    case fileNotFound = "file_not_found"
    case permissionDenied = "permission_denied"
    case unknownError = "unknown_error"
    
    // Date & Time
    case today = "today"
    case yesterday = "yesterday"
    case tomorrow = "tomorrow"
    case thisWeek = "this_week"
    case thisMonth = "this_month"
    case selectDate = "select_date"
    case selectTime = "select_time"
    case dueDate = "due_date"
    case startDate = "start_date"
    case endDate = "end_date"
    
    // Tags & Categories
    case tags = "tags"
    case addTag = "add_tag"
    case removeTag = "remove_tag"
    case category = "category"
    case categories = "categories"
    case selectCategory = "select_category"
    
    // Subscription
    case subscribe = "subscribe"
    case upgrade = "upgrade"
    case premium = "premium"
    case freeVersion = "free_version"
    case subscriptionExpired = "subscription_expired"
    
    // Main Interface
    case mine = "mine"
    case all = "all"
    case favorites = "favorites"
    case images = "images"
    case planned = "planned"
    case uncategorized = "uncategorized"
    
    // Input Related
    case addTagPlaceholder = "add_tag_placeholder"
    case amountPlaceholder = "amount_placeholder"
    case pomodoroStepper = "pomodoro_stepper"
    
    // Accounting Related
    case expense = "expense"
    case income = "income"
    case accountingInfo = "accounting_info"
    case currencySymbol = "currency_symbol"
    
    // Pomodoro Related
    case pomodoroPlanned = "pomodoro_planned"
    case pomodoroCompleted = "pomodoro_completed"
    case pomodoroSection = "pomodoro_section"
    
    // Project Related
    case noProject = "no_project"
    case projectSection = "project_section"
    
    // Statistics Charts
    case projectProgress = "project_progress"
    case completionTrend = "completion_trend"
    case timeDistribution = "time_distribution"
    case pomodoroEfficiency = "pomodoro_efficiency"
    case tagUsageStats = "tag_usage_stats"
    case chartInDevelopment = "chart_in_development"
    
    // Upgrade Prompts
    case upgradeToPremium = "upgrade_to_premium"
    case unlockAllFeatures = "unlock_all_features"
    case unlockHistory = "unlock_history"
    case unlockEditing = "unlock_editing"
    case freeLimitMessage = "free_limit_message"
    case viewOnly = "view_only"
    
    // Language Settings
    case languageSelection = "language_selection"
    case chineseSimplified = "chinese_simplified"
    case chineseTraditional = "chinese_traditional"
    case english = "english"
    case japanese = "japanese"
    
    var localizedString: String {
        NSLocalizedString(self.rawValue, comment: "")
    }
}

// MARK: - Localization Manager
final class LocalizationManager: ObservableObject {
    static let shared = LocalizationManager()
    
    @Published var currentLanguage: Language = .system {
        didSet {
            updateCurrentBundle()
            DispatchQueue.main.async {
                self.objectWillChange.send()
            }
        }
    }
    
    private var currentBundle: Bundle = Bundle.main
    
    var currentLocale: Locale {
        let languageCode = currentLanguage == .system ? 
            Locale.current.language.languageCode?.identifier ?? "zh-Hans" : 
            currentLanguage.code
        return Locale(identifier: languageCode)
    }
    
    private init() {
        setupInitialLanguage()
        updateCurrentBundle()
    }
    
    private func setupInitialLanguage() {
        if let savedLanguage = UserDefaults.standard.string(forKey: "selectedLanguage"),
           let language = Language(rawValue: savedLanguage) {
            currentLanguage = language
        } else {
            currentLanguage = .system
        }
    }
    
    private func updateCurrentBundle() {
        let languageCode = currentLanguage == .system ? 
            Locale.current.language.languageCode?.identifier ?? "zh-Hans" : 
            currentLanguage.code
            
        if let path = Bundle.main.path(forResource: languageCode, ofType: "lproj"),
           let bundle = Bundle(path: path) {
            currentBundle = bundle
        } else {
            // 回退到中文
            if let path = Bundle.main.path(forResource: "zh-Hans", ofType: "lproj"),
               let bundle = Bundle(path: path) {
                currentBundle = bundle
            } else {
                currentBundle = Bundle.main
            }
        }
    }
    
    func setLanguage(_ language: Language) {
        currentLanguage = language
        UserDefaults.standard.set(language.rawValue, forKey: "selectedLanguage")
        
        // 发送通知，UI将自动更新
        NotificationCenter.default.post(name: .languageChanged, object: nil)
    }
    
    func localizedString(for key: LocalizedStringKey) -> String {
        return localizedString(for: key.rawValue)
    }
    
    func localizedString(for key: String) -> String {
        return currentBundle.localizedString(forKey: key, value: key, table: nil)
    }
}

// MARK: - Language Enum
enum Language: String, CaseIterable, Identifiable {
    case system = "system"
    case chineseSimplified = "zh-Hans"
    case chineseTraditional = "zh-Hant"
    case english = "en"
    case japanese = "ja"
    case korean = "ko"
    
    var id: String { rawValue }
    
    var displayName: String {
        switch self {
        case .system:
            return NSLocalizedString("system_language", comment: "")
        case .chineseSimplified:
            return LocalizedStringKey.chineseSimplified.localizedString
        case .chineseTraditional:
            return LocalizedStringKey.chineseTraditional.localizedString
        case .english:
            return LocalizedStringKey.english.localizedString
        case .japanese:
            return LocalizedStringKey.japanese.localizedString
        case .korean:
            return "한국어"
        }
    }
    
    var code: String {
        switch self {
        case .system:
            return Locale.current.language.languageCode?.identifier ?? "zh-Hans"
        case .chineseSimplified:
            return "zh-Hans"
        case .chineseTraditional:
            return "zh-Hant"
        case .english:
            return "en"
        case .japanese:
            return "ja"
        case .korean:
            return "ko"
        }
    }
    
    var flag: String {
        switch self {
        case .system:
            return "🌐"
        case .chineseSimplified:
            return "🇨🇳"
        case .chineseTraditional:
            return "🇹🇼"
        case .english:
            return "🇺🇸"
        case .japanese:
            return "🇯🇵"
        case .korean:
            return "🇰🇷"
        }
    }
}

// MARK: - Notification Extension
extension Notification.Name {
    static let languageChanged = Notification.Name("languageChanged")
}

// MARK: - SwiftUI Extensions
extension View {
    func localized(_ key: LocalizedStringKey) -> some View {
        Text(key.localizedString)
    }
} 