import SwiftUI
import SwiftData

@MainActor
class FixedChatViewModel: ObservableObject {
    @Published var messages: [ChatItem] = []
    @Published var isLoadingHistory = false
    @Published var hasMoreHistory = true
    @Published var isInitialLoading = true
    
    private let dataManager: DataManager
    private let pageSize = 20
    
    // 基于时间戳的分页游标
    private var oldestMessageTimestamp: Date?
    
    // 过滤条件缓存
    private var currentProject: Project?
    private var currentFilter: ContentFilter?
    private var currentSelectedDate: Date?
    private var currentSelectedItemType: ItemType?
    private var currentSelectedTags: Set<String> = []
    private var currentIsViewingAllData: Bool = true
    private var currentIsPlannedView: Bool = false
    
    init(dataManager: DataManager = DataManager()) {
        self.dataManager = dataManager
        print("FixedChatViewModel initialized")
    }
    
    func setContext(_ context: ModelContext) {
        dataManager.setContext(context)
    }
    
    // MARK: - 核心方法：加载最新消息
    func loadLatestMessages(
        project: Project? = nil,
        filter: ContentFilter? = nil,
        selectedDate: Date? = nil,
        selectedItemType: ItemType? = nil,
        selectedTags: Set<String> = [],
        isViewingAllData: Bool = true,
        isPlannedView: Bool = false
    ) async {
        print("🚀 开始加载最新消息")
        
        // 保存过滤条件
        saveCurrentFilters(
            project: project,
            filter: filter,
            selectedDate: selectedDate,
            selectedItemType: selectedItemType,
            selectedTags: selectedTags,
            isViewingAllData: isViewingAllData,
            isPlannedView: isPlannedView
        )
        
        isInitialLoading = true
        isLoadingHistory = false
        
        // 获取最新的消息
        let latestMessages = await fetchLatestMessages()
        
        // 更新状态 - 反转为晚→早顺序用于聊天界面显示
        messages = latestMessages.reversed()
        updateOldestTimestamp()
        hasMoreHistory = latestMessages.count == pageSize
        isInitialLoading = false
        
        print("✅ 加载完成: \(messages.count)条消息, hasMore: \(hasMoreHistory)")
        print("📅 最早消息时间: \(oldestMessageTimestamp?.description ?? "nil")")
        if let first = messages.first, let last = messages.last {
            print("📊 消息范围: 最新[\(first.text.prefix(20))] -> 最旧[\(last.text.prefix(20))]")
        }
    }
    
    // MARK: - 核心方法：加载历史消息
    func loadHistoryMessages() async {
        guard !isLoadingHistory && hasMoreHistory && !isInitialLoading else {
            print("⏸️ 跳过历史加载: loading=\(isLoadingHistory), hasMore=\(hasMoreHistory), initial=\(isInitialLoading)")
            return
        }
        
        guard let oldestTimestamp = oldestMessageTimestamp else {
            print("❌ 没有时间戳游标，无法加载历史")
            return
        }
        
        print("📖 开始加载历史消息，时间戳前: \(oldestTimestamp)")
        isLoadingHistory = true
        
        // 获取指定时间戳之前的消息
        let historyMessages = await fetchMessagesBeforeTimestamp(oldestTimestamp)
        
        if !historyMessages.isEmpty {
            // 插入到数组末尾（因为messages现在是晚→早顺序，历史消息应该在末尾）
            messages.append(contentsOf: historyMessages.reversed())
            updateOldestTimestamp()
            hasMoreHistory = historyMessages.count == pageSize
            
            print("✅ 历史加载完成: 新增\(historyMessages.count)条, 总计\(messages.count)条")
            print("📅 新的最早时间: \(oldestMessageTimestamp?.description ?? "nil")")
        } else {
            hasMoreHistory = false
            print("🚫 没有更多历史消息")
        }
        
        isLoadingHistory = false
    }
    
    // MARK: - 重置状态
    func resetState() {
        messages = []
        oldestMessageTimestamp = nil
        hasMoreHistory = true
        isLoadingHistory = false
        isInitialLoading = true
        print("🔄 状态已重置")
    }
    
    // MARK: - 私有方法
    private func saveCurrentFilters(
        project: Project?,
        filter: ContentFilter?,
        selectedDate: Date?,
        selectedItemType: ItemType?,
        selectedTags: Set<String>,
        isViewingAllData: Bool,
        isPlannedView: Bool
    ) {
        currentProject = project
        currentFilter = filter
        currentSelectedDate = selectedDate
        currentSelectedItemType = selectedItemType
        currentSelectedTags = selectedTags
        currentIsViewingAllData = isViewingAllData
        currentIsPlannedView = isPlannedView
    }
    
    private func fetchLatestMessages() async -> [ChatItem] {
        let hasComplexFilter = currentProject != nil ||
                              (currentFilter != nil && currentFilter != .all) ||
                              currentSelectedItemType != nil ||
                              !currentSelectedTags.isEmpty ||
                              !currentIsViewingAllData
        
        if hasComplexFilter {
            return await dataManager.fetchItemsWithFilterAndDate(
                project: currentProject,
                filter: currentFilter,
                selectedDate: currentSelectedDate,
                selectedItemType: currentSelectedItemType,
                selectedTags: currentSelectedTags,
                isViewingAllData: currentIsViewingAllData,
                isPlannedView: currentIsPlannedView,
                beforeDate: nil,
                limit: pageSize
            )
        } else {
            return await dataManager.fetchLatestItems(limit: pageSize)
        }
    }
    
    private func fetchMessagesBeforeTimestamp(_ timestamp: Date) async -> [ChatItem] {
        let hasComplexFilter = currentProject != nil ||
                              (currentFilter != nil && currentFilter != .all) ||
                              currentSelectedItemType != nil ||
                              !currentSelectedTags.isEmpty ||
                              !currentIsViewingAllData
        
        if hasComplexFilter {
            return await dataManager.fetchItemsWithFilterAndDate(
                project: currentProject,
                filter: currentFilter,
                selectedDate: currentSelectedDate,
                selectedItemType: currentSelectedItemType,
                selectedTags: currentSelectedTags,
                isViewingAllData: currentIsViewingAllData,
                isPlannedView: currentIsPlannedView,
                beforeDate: timestamp,
                limit: pageSize
            )
        } else {
            return await dataManager.fetchItemsBefore(date: timestamp, limit: pageSize)
        }
    }
    
    private func updateOldestTimestamp() {
        // 数组现在是按时间倒序排列（晚→早），所以最后一个是最早的
        oldestMessageTimestamp = messages.last?.effectiveTimestamp
    }
}