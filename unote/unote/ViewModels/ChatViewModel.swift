import SwiftUI
import SwiftData

@MainActor
class ChatViewModel: ObservableObject {
    @Published var items: [ChatItem] = []
    @Published var isLoading = false
    @Published var hasMoreItems = true
    
    private let dataManager: DataManager
    private let pageSize = 20
    private var currentOffset = 0
    private var isInitialLoad = true
    
    // 当前过滤条件
    private var currentProject: Project?
    private var currentFilter: ContentFilter?
    private var currentSelectedDate: Date?
    private var currentSelectedItemType: ItemType?
    private var currentSelectedTags: Set<String> = []
    private var currentIsViewingAllData: Bool = true
    private var currentIsPlannedView: Bool = false

    init(dataManager: DataManager = DataManager()) {
        self.dataManager = dataManager
        print("ChatViewModel initialized")
    }

    func setContext(_ context: ModelContext) {
        print("Setting context in ChatViewModel")
        dataManager.setContext(context)
        Task {
            await loadInitialItems()
        }
    }
    
    func loadInitialItems(
        project: Project? = nil,
        filter: ContentFilter? = nil,
        selectedDate: Date? = nil,
        selectedItemType: ItemType? = nil,
        selectedTags: Set<String> = [],
        isViewingAllData: Bool = true,
        isPlannedView: Bool = false
    ) async {
        print("Loading initial items with filters")
        isLoading = true
        
        // 保存当前过滤条件
        currentProject = project
        currentFilter = filter
        currentSelectedDate = selectedDate
        currentSelectedItemType = selectedItemType
        currentSelectedTags = selectedTags
        currentIsViewingAllData = isViewingAllData
        currentIsPlannedView = isPlannedView
        
        let latestItems: [ChatItem]
        
        // 如果有过滤条件，使用带过滤的查询
        let hasComplexFilter = project != nil || 
                              (filter != nil && filter != .all) || 
                              selectedItemType != nil || 
                              !selectedTags.isEmpty || 
                              !isViewingAllData
        
        if hasComplexFilter {
            latestItems = await dataManager.fetchItemsWithFilter(
                project: project,
                filter: filter,
                selectedDate: selectedDate,
                selectedItemType: selectedItemType,
                selectedTags: selectedTags,
                isViewingAllData: isViewingAllData,
                isPlannedView: isPlannedView,
                offset: 0,
                limit: pageSize
            )
        } else {
            // 无过滤条件时使用简单查询
            latestItems = await dataManager.fetchLatestItems(limit: pageSize)
        }
        
        items = latestItems
        currentOffset = latestItems.count
        hasMoreItems = latestItems.count == pageSize
        isInitialLoad = false
        isLoading = false
        
        print("Loaded \(items.count) initial items, hasMoreItems: \(hasMoreItems), pageSize: \(pageSize), hasComplexFilter: \(hasComplexFilter)")
    }

    func fetchItems() async {
        print("Fetching items")
        items = await dataManager.fetchItems()
        print("Fetched \(items.count) items")
    }
    
    func loadMoreItems() async {
        guard !isLoading && hasMoreItems else { 
            print("跳过加载更多: isLoading=\(isLoading), hasMoreItems=\(hasMoreItems)")
            return 
        }
        
        print("开始加载更多历史项目，当前偏移量: \(currentOffset)")
        isLoading = true
        
        let moreItems: [ChatItem]
        
        // 使用当前的过滤条件加载更多数据
        let hasComplexFilter = currentProject != nil || 
                              (currentFilter != nil && currentFilter != .all) || 
                              currentSelectedItemType != nil || 
                              !currentSelectedTags.isEmpty || 
                              !currentIsViewingAllData
        
        if hasComplexFilter {
            moreItems = await dataManager.fetchItemsWithFilter(
                project: currentProject,
                filter: currentFilter,
                selectedDate: currentSelectedDate,
                selectedItemType: currentSelectedItemType,
                selectedTags: currentSelectedTags,
                isViewingAllData: currentIsViewingAllData,
                isPlannedView: currentIsPlannedView,
                offset: currentOffset,
                limit: pageSize
            )
        } else {
            moreItems = await dataManager.fetchItemsWithPagination(offset: currentOffset, limit: pageSize)
        }
        
        if !moreItems.isEmpty {
            // 将历史消息插入到数组前面（因为是向上滑动加载历史）
            items.insert(contentsOf: moreItems, at: 0)
            currentOffset += moreItems.count
            hasMoreItems = moreItems.count == pageSize
        } else {
            hasMoreItems = false
        }
        
        isLoading = false
        print("加载更多完成: 新增\(moreItems.count)项, 总计\(items.count)项, 还有更多: \(hasMoreItems)")
    }
    
    func resetPagination() {
        currentOffset = 0
        hasMoreItems = true
        items = []
    }

    func addItem(text: String, type: ItemType, tags: [String], pomodoroCount: Int?) {
        print("Adding new item: \(text)")
        let newItem = ChatItem(text: text, type: type, tags: tags, pomodoroCount: pomodoroCount!)
        Task {
            await dataManager.addItem(newItem)
            resetPagination()
            await loadInitialItems(
                project: currentProject,
                filter: currentFilter,
                selectedDate: currentSelectedDate,
                selectedItemType: currentSelectedItemType,
                selectedTags: currentSelectedTags,
                isViewingAllData: currentIsViewingAllData,
                isPlannedView: currentIsPlannedView
            )
            print("Items count after adding: \(items.count)")
        }
    }
}
