import SwiftUI
import SwiftData

@MainActor
class SimplestChatViewModel: ObservableObject {
    @Published var messages: [ChatItem] = []
    @Published var isLoadingHistory = false
    @Published var hasMoreHistory = true
    @Published var isNewMessageAdded = false  // 新增：标记是否有新消息添加
    
    private let dataManager: DataManager
    private let batchSize = 25  // 优化批次大小平衡性能和内存使用
    private var oldestTimestamp: Date?
    
    // 过滤条件
    private var currentProject: Project?
    private var currentFilter: ContentFilter?
    private var currentSelectedDate: Date?
    private var currentSelectedItemType: ItemType?
    private var currentSelectedTags: Set<String> = []
    private var currentIsViewingAllData: Bool = true
    private var currentIsPlannedView: Bool = false
    
    init(dataManager: DataManager = DataManager()) {
        self.dataManager = dataManager
        print("💡 SimplestChatViewModel initialized")
    }
    
    deinit {
        print("🗑️ SimplestChatViewModel deinit - 清理资源")
        // Note: Cannot access @MainActor properties in deinit
        // Memory will be automatically released when the object is deallocated
    }
    
    func setContext(_ context: ModelContext) {
        dataManager.setContext(context)
    }
    
    // MARK: - 初始加载最新消息
    @MainActor
    func loadInitialMessages(
        project: Project? = nil,
        filter: ContentFilter? = nil,
        selectedDate: Date? = nil,
        selectedItemType: ItemType? = nil,
        selectedTags: Set<String> = [],
        isViewingAllData: Bool = true,
        isPlannedView: Bool = false
    ) async {
        print("🚀 开始初始加载")
        
        // 保存过滤条件
        saveCurrentFilters(
            project: project,
            filter: filter,
            selectedDate: selectedDate,
            selectedItemType: selectedItemType,
            selectedTags: selectedTags,
            isViewingAllData: isViewingAllData,
            isPlannedView: isPlannedView
        )
        
        // 重置状态
        messages = []
        oldestTimestamp = nil
        hasMoreHistory = true
        isLoadingHistory = false
        isNewMessageAdded = false
        
        // 获取最新消息
        let newMessages = await fetchMessages(beforeDate: nil)
        let hasComplexFilter = currentProject != nil ||
                              (currentFilter != nil && currentFilter != .all) ||
                              currentSelectedItemType != nil ||
                              !currentSelectedTags.isEmpty ||
                              !currentIsViewingAllData
        
        // 按显示顺序排列：最旧在前，最新在后（用于UI显示）
        messages = newMessages.sorted { $0.effectiveTimestamp < $1.effectiveTimestamp }
        
        // 更新游标
        updateOldestTimestamp()
        hasMoreHistory = newMessages.count == batchSize
        
        print("✅ 初始加载完成: \(messages.count)条消息")
        print("🔍 过滤条件: project=\(currentProject?.name ?? "nil"), filter=\(currentFilter.self ?? .all)")
        print("🔍 查询参数: isViewingAllData=\(currentIsViewingAllData), hasComplexFilter=\(hasComplexFilter)")
        if let oldest = messages.first, let newest = messages.last {
            print("📊 时间范围: \(oldest.effectiveTimestamp) → \(newest.effectiveTimestamp)")
        }
    }
    
    // MARK: - 加载历史消息
    @MainActor
    func loadHistoryMessages() async {
        guard !isLoadingHistory && hasMoreHistory,
              let beforeDate = oldestTimestamp else {
            print("⏸️ 跳过历史加载")
            return
        }
        
        print("📖 开始加载历史消息，时间点前: \(beforeDate)")
        isLoadingHistory = true
        
        // 获取更早的消息
        let historyMessages = await fetchMessages(beforeDate: beforeDate)
        
        if !historyMessages.isEmpty {
            // 按时间排序
            let sortedHistory = historyMessages.sorted { $0.effectiveTimestamp < $1.effectiveTimestamp }
            
            // 插入到现有消息前面（更早的消息）
            messages.insert(contentsOf: sortedHistory, at: 0)
            
            // 更新游标
            updateOldestTimestamp()
            hasMoreHistory = historyMessages.count == batchSize
            
            print("✅ 历史加载完成: 新增\(historyMessages.count)条, 总计\(messages.count)条")
        } else {
            hasMoreHistory = false
            print("🚫 没有更多历史消息")
        }
        
        isLoadingHistory = false
    }
    
    // MARK: - 重置状态
    @MainActor
    func reset() {
        let resetStartTime = CFAbsoluteTimeGetCurrent()
        print("🔄 ViewModel状态重置开始 - 当前消息数: \(messages.count)")
        
        messages.removeAll(keepingCapacity: false)  // 不保留容量以释放内存
        oldestTimestamp = nil
        hasMoreHistory = true
        isLoadingHistory = false
        isNewMessageAdded = false
        
        // 清理过滤条件以释放引用
        currentProject = nil
        currentFilter = nil
        currentSelectedDate = nil
        currentSelectedItemType = nil
        currentSelectedTags.removeAll()
        currentIsViewingAllData = true
        currentIsPlannedView = false
        
        let resetEndTime = CFAbsoluteTimeGetCurrent()
        print("🔄 ViewModel状态重置完成, 耗时: \(String(format: "%.2f", (resetEndTime - resetStartTime) * 1000))ms")
    }
    
    // MARK: - 标记新消息添加
    @MainActor
    func markNewMessageAdded() {
        isNewMessageAdded = true
        print("✉️ 标记有新消息添加")
    }
    
    // MARK: - 私有方法
    private func saveCurrentFilters(
        project: Project?,
        filter: ContentFilter?,
        selectedDate: Date?,
        selectedItemType: ItemType?,
        selectedTags: Set<String>,
        isViewingAllData: Bool,
        isPlannedView: Bool
    ) {
        currentProject = project
        currentFilter = filter
        currentSelectedDate = selectedDate
        currentSelectedItemType = selectedItemType
        currentSelectedTags = selectedTags
        currentIsViewingAllData = isViewingAllData
        currentIsPlannedView = isPlannedView
    }
    
    private func fetchMessages(beforeDate: Date?) async -> [ChatItem] {
        let hasComplexFilter = currentProject != nil ||
                              (currentFilter != nil && currentFilter != .all) ||
                              currentSelectedItemType != nil ||
                              !currentSelectedTags.isEmpty ||
                              !currentIsViewingAllData
        
        if hasComplexFilter {
            return await dataManager.fetchItemsWithFilterAndDate(
                project: currentProject,
                filter: currentFilter,
                selectedDate: currentSelectedDate,
                selectedItemType: currentSelectedItemType,
                selectedTags: currentSelectedTags,
                isViewingAllData: currentIsViewingAllData,
                isPlannedView: currentIsPlannedView,
                beforeDate: beforeDate,
                limit: batchSize
            )
        } else {
            if let beforeDate = beforeDate {
                return await dataManager.fetchItemsBefore(date: beforeDate, limit: batchSize)
            } else {
                return await dataManager.fetchLatestItems(limit: batchSize)
            }
        }
    }
    
    @MainActor
    private func updateOldestTimestamp() {
        // messages已按时间升序排列，第一个是最旧的
        oldestTimestamp = messages.first?.effectiveTimestamp
    }
}
