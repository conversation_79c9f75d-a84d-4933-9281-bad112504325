import SwiftUI
import SwiftData

@MainActor
class SimpleChatViewModel: ObservableObject {
    @Published var messages: [ChatItem] = []
    @Published var isLoadingHistory = false
    @Published var hasMoreHistory = true
    @Published var isInitialLoading = true
    @Published var lastChangeWasNewMessage = false  // 新增：标记最后一次变化是否为新消息
    
    private let dataManager: DataManager
    private let pageSize = 30  // 增加批次大小，减少加载频率
    
    // 简化：只保存最旧消息的时间戳作为分页游标
    private var oldestTimestamp: Date?
    
    // 当前过滤条件
    private var currentProject: Project?
    private var currentFilter: ContentFilter?
    private var currentSelectedDate: Date?
    private var currentSelectedItemType: ItemType?
    private var currentSelectedTags: Set<String> = []
    private var currentIsViewingAllData: Bool = true
    private var currentIsPlannedView: Bool = false
    
    init(dataManager: DataManager = DataManager()) {
        self.dataManager = dataManager
    }
    
    func setContext(_ context: ModelContext) {
        dataManager.setContext(context)
    }
    
    // MARK: - 核心方法：初始加载
    func loadLatestMessages(
        project: Project? = nil,
        filter: ContentFilter? = nil,
        selectedDate: Date? = nil,
        selectedItemType: ItemType? = nil,
        selectedTags: Set<String> = [],
        isViewingAllData: Bool = true,
        isPlannedView: Bool = false
    ) async {
        print("🚀 开始加载最新消息")
        
        // 保存过滤条件
        saveFilters(
            project: project,
            filter: filter,
            selectedDate: selectedDate,
            selectedItemType: selectedItemType,
            selectedTags: selectedTags,
            isViewingAllData: isViewingAllData,
            isPlannedView: isPlannedView
        )
        
        isInitialLoading = true
        isLoadingHistory = false
        
        // 获取最新消息
        let latestItems = await fetchMessages(beforeDate: nil)
        
        // 直接按正确顺序设置：最新在前，最旧在后
        messages = latestItems.sorted { $0.effectiveTimestamp > $1.effectiveTimestamp }
        
        // 更新分页游标
        oldestTimestamp = messages.last?.effectiveTimestamp
        hasMoreHistory = latestItems.count == pageSize
        isInitialLoading = false
        lastChangeWasNewMessage = false  // 初始加载不是新消息
        
        print("✅ 初始加载完成: \(messages.count)条消息")
        if let newest = messages.first, let oldest = messages.last {
            print("📊 消息范围: \(newest.effectiveTimestamp) → \(oldest.effectiveTimestamp)")
        }
    }
    
    // MARK: - 核心方法：加载历史
    func loadHistoryMessages() async {
        guard !isLoadingHistory && hasMoreHistory && !isInitialLoading,
              let beforeDate = oldestTimestamp else {
            return
        }
        
        print("📖 开始加载历史消息")
        isLoadingHistory = true
        
        // 获取更早的消息
        let historyItems = await fetchMessages(beforeDate: beforeDate)
        
        if !historyItems.isEmpty {
            // 添加到现有消息后面（时间更早）
            let sortedHistory = historyItems.sorted { $0.effectiveTimestamp > $1.effectiveTimestamp }
            messages.append(contentsOf: sortedHistory)
            
            // 更新分页游标
            oldestTimestamp = messages.last?.effectiveTimestamp
            hasMoreHistory = historyItems.count == pageSize
            lastChangeWasNewMessage = false  // 历史加载不是新消息
            
            print("✅ 历史加载完成: 新增\(historyItems.count)条")
        } else {
            hasMoreHistory = false
            lastChangeWasNewMessage = false
            print("🚫 没有更多历史消息")
        }
        
        isLoadingHistory = false
    }
    
    // MARK: - 重置
    func resetState() {
        messages = []
        oldestTimestamp = nil
        hasMoreHistory = true
        isLoadingHistory = false
        isInitialLoading = true
        lastChangeWasNewMessage = false
    }
    
    // MARK: - 添加新消息
    func addNewMessage() {
        // 当有新消息时调用此方法
        lastChangeWasNewMessage = true
    }
    
    // MARK: - 私有方法
    private func saveFilters(
        project: Project?,
        filter: ContentFilter?,
        selectedDate: Date?,
        selectedItemType: ItemType?,
        selectedTags: Set<String>,
        isViewingAllData: Bool,
        isPlannedView: Bool
    ) {
        currentProject = project
        currentFilter = filter
        currentSelectedDate = selectedDate
        currentSelectedItemType = selectedItemType
        currentSelectedTags = selectedTags
        currentIsViewingAllData = isViewingAllData
        currentIsPlannedView = isPlannedView
    }
    
    private func fetchMessages(beforeDate: Date?) async -> [ChatItem] {
        // 检查是否有复杂过滤条件
        let hasComplexFilter = currentProject != nil ||
                              (currentFilter != nil && currentFilter != .all) ||
                              currentSelectedItemType != nil ||
                              !currentSelectedTags.isEmpty ||
                              !currentIsViewingAllData
        
        if hasComplexFilter {
            return await dataManager.fetchItemsWithFilterAndDate(
                project: currentProject,
                filter: currentFilter,
                selectedDate: currentSelectedDate,
                selectedItemType: currentSelectedItemType,
                selectedTags: currentSelectedTags,
                isViewingAllData: currentIsViewingAllData,
                isPlannedView: currentIsPlannedView,
                beforeDate: beforeDate,
                limit: pageSize
            )
        } else {
            if let beforeDate = beforeDate {
                return await dataManager.fetchItemsBefore(date: beforeDate, limit: pageSize)
            } else {
                return await dataManager.fetchLatestItems(limit: pageSize)
            }
        }
    }
}