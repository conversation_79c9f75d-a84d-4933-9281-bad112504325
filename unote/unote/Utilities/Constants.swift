import SwiftUI

enum Constants {
    static let maxPomodoroCount = 10
    static let defaultPomodoroCount = 1
    
    enum Colors {
        static let noteBackground = Color.blue.opacity(0.1)
        static let taskBackground = Color.green.opacity(0.1)
    }
    
    enum Strings {
            static let appName = "app_name".localized
    static let inputPlaceholder = "input_placeholder".localized
    static let sendButton = "send_button".localized
            static let noteType = "note_type".localized
    static let taskType = "task_type".localized
    static let pomodoroLabel = "pomodoro_label".localized
    }
}