import SwiftUI
import SwiftData

struct HomeView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.horizontalSizeClass) private var horizontalSizeClass
    @Query private var projects: [Project]
    @Query(sort: \ChatItem.timestamp, order: .reverse) 
    private var allChatItems: [ChatItem]
    @StateObject private var viewModel = HomeViewModel()
    
    var body: some View {
        NavigationSplitView {
            NavigationStack(path: $viewModel.uiStateManager.navigationPath) {
                mainContent
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar { toolbarContent }
                    .searchable(text: $viewModel.searchViewModel.searchText, prompt: "search_placeholder".localized)
                    .onReceive(viewModel.searchViewModel.$searchText.debounce(for: .milliseconds(300), scheduler: DispatchQueue.main)) { newValue in
                        viewModel.updateChatItems(allChatItems)
                    }
                    .searchSuggestions { searchSuggestions }
                    .navigationDestination(for: UIStateManager.NavigationDestination.self) { destination in
                        switch destination {
                        case .content(let project, let filter, let scrollToItem):
                            ContentView(project: project, filter: filter, scrollToItem: scrollToItem)
                        }
                    }
            }
        } detail: {
            detailView
        }
        .sheet(isPresented: $viewModel.uiStateManager.showingAddProject) {
            AddProjectView()
        }
        .sheet(isPresented: $viewModel.uiStateManager.showingSettings) {
            SettingsView()
        }
        .sheet(item: $viewModel.uiStateManager.editingProject) { project in
            EditProjectView(project: project)
        }
        .sheet(isPresented: $viewModel.uiStateManager.showingStats) {
            StatsView(selectedDate: .constant(Date()), project: nil)
        }
        .onChange(of: viewModel.inputStateManager.selectedFilter) { oldValue, newValue in
            updateSelectedItemType(for: newValue)
        }
    }
    
    // MARK: - 主要内容视图
    private var mainContent: some View {
        VStack(spacing: 0) {
            ScrollView {
                LazyVStack(spacing: 8, pinnedViews: []) {
                    // 日期统计卡片
                    HomeStatisticsView(
                        allChatItems: allChatItems,
                        showingStats: $viewModel.uiStateManager.showingStats
                    )
                    .padding(.horizontal, 16)
                    
                    // 分类网格
                    HomeCategoryView(allChatItems: allChatItems)
                    
                    // 项目列表
                    HomeProjectListView(
                        projects: projects,
                        searchText: viewModel.searchViewModel.searchText,
                        onEdit: viewModel.handleProjectEdit
                    )
                    .padding(.top, 4)
                }
            }
            
            // 输入区域（仅在紧凑布局下显示）
            if horizontalSizeClass == .compact {
                InputAreaView(
                    inputText: $viewModel.inputStateManager.inputText,
                    inputAmount: $viewModel.inputStateManager.inputAmount,
                    selectedType: $viewModel.inputStateManager.selectedType,
                    pomodoroCount: $viewModel.inputStateManager.pomodoroCount,
                    tags: $viewModel.inputStateManager.tags,
                    selectedImages: $viewModel.inputStateManager.selectedImages,
                    selectedProject: $viewModel.uiStateManager.selectedProject,
                    selectedFilter: $viewModel.inputStateManager.selectedFilter,
                    currentProject: nil,
                    onSend: viewModel.handleSend
                )
                .background(Color(.systemBackground))
            }
        }
        .background(Color(.systemBackground))
    }
    
    // MARK: - 工具栏内容
    @ToolbarContentBuilder
    private var toolbarContent: some ToolbarContent {
        ToolbarItem(placement: .navigationBarLeading) {
            Text(greetingText)
                .font(.system(size: 16, weight: .bold))
                .foregroundStyle(.primary)
                .transition(.opacity)
                .padding(.horizontal, 4)
        }
        
        ToolbarItem(placement: .navigationBarTrailing) {
            HStack(spacing: 16) {
                Button(action: { viewModel.uiStateManager.showingAddProject = true }) {
                    Image(systemName: "plus")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                }
                
                Button(action: { viewModel.uiStateManager.showingSettings = true }) {
                    Image(systemName: "gear")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                }
            }
        }
    }
    
    // MARK: - 搜索建议
    @ViewBuilder
    private var searchSuggestions: some View {
        if !viewModel.searchViewModel.searchText.isEmpty {
            ForEach(viewModel.searchViewModel.searchResults, id: \.1.id) { project, item in
                SearchResultRow(project: project, item: item)
                    .onTapGesture {
                        viewModel.navigateToSearchResult(project: project, item: item)
                    }
            }
        }
    }
    
    // MARK: - 详情视图
    private var detailView: some View {
        Group {
            if viewModel.uiStateManager.navigationPath.isEmpty {
                if let project = viewModel.uiStateManager.selectedProject {
                    ContentView(project: project, filter: .project, scrollToItem: nil)
                } else {
                    ContentView(project: nil, filter: viewModel.uiStateManager.selectedContentFilter, scrollToItem: nil)
                }
            }
        }
    }
    
    // MARK: - 搜索管理器
    private var searchManager: HomeSearchView {
        HomeSearchView(
            searchText: $viewModel.searchViewModel.searchText,
            searchResults: viewModel.searchViewModel.searchResults,
            navigationPath: $viewModel.uiStateManager.navigationPath,
            allChatItems: allChatItems
        )
    }
    
    // MARK: - 辅助方法
    private var greetingText: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 0..<6: return "late_night_greeting".localized
        case 6..<9: return "good_morning".localized
        case 9..<12: return "good_forenoon".localized
        case 12..<14: return "good_noon".localized
        case 14..<18: return "good_afternoon".localized
        case 18..<22: return "good_evening".localized
        default: return "good_night".localized
        }
    }
    
    private func updateSelectedItemType(for filter: ItemFilter) {
        viewModel.inputStateManager.updateSelectedType(for: filter)
        switch filter {
        case .tasks: viewModel.uiStateManager.selectedItemType = .task
        case .notes: viewModel.uiStateManager.selectedItemType = .note
        case .all: viewModel.uiStateManager.selectedItemType = nil
        }
    }
}


