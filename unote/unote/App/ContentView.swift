import SwiftUI
import SwiftData
import Foundation

enum ContentFilter {
    case all
    case uncategorized
    case project
    case images
    case planned
    case favorites
    case pomodoro
}

enum ItemFilter: String, CaseIterable, Identifiable {
    case all = "all"
    case tasks = "tasks"
    case notes = "notes"
    
    var id: String { self.rawValue } 
    
    var description: String {
        switch self {
        case .all: return "all".localized
        case .tasks: return "task_type".localized
        case .notes: return "note_type".localized
        }
    }
    
    var iconName: String {
        switch self {
        case .all: return "tray.full"
        case .tasks: return "checklist"
        case .notes: return "note.text"
        }
    }
}

// 添加一个新的环境键
private struct IsAllViewKey: EnvironmentKey {
    static let defaultValue = false
}

extension EnvironmentValues {
    var isAllView: Bool {
        get { self[IsAllViewKey.self] }
        set { self[IsAllViewKey.self] = newValue }
    }
}

// 将扩展移到文件级别
extension ContentFilter {
    func matches(item: ChatItem, project: Project?) -> Bool {
        switch self {
        case .all:
            return true
        case .uncategorized:
            return item.project == nil
        case .project:
            return item.project?.id == project?.id
        case .images:
            return !item.imageData.isEmpty
        case .planned:
            return item.plannedDate != nil
        case .favorites:
            return item.isFavorite
        case .pomodoro:
            return (item.pomodoroCount ?? 0) > 0 || item.completedPomodoros > 0
        }
    }
}

struct ContentView: View {
    @Environment(\.presentationMode) var presentationMode
    let project: Project?
    let filter: ContentFilter
    @Query private var allItems: [ChatItem]
    @Environment(\.modelContext) private var modelContext
    @State private var chatItems: [ChatItem] = []
    @StateObject private var chatViewModel = ChatViewModel()
    @StateObject private var simplestChatViewModel = SimplestChatViewModel()
    @FocusState private var isInputFocused: Bool
    
    @State private var inputText = ""
    @State private var inputAmount = "" // 新增金额输入状态
    @State private var selectedType: ItemType = .note
    @State private var pomodoroCount = 0
    @State private var tags: [String] = []
    @State private var selectedImages: [UIImage] = []
    @State private var selectedImage: UIImage?
    @State private var selectedFilter: ItemFilter = .all
    @State private var selectedDate: Date = Date()
    @State private var selectedProject: Project? // 新增
    @AppStorage("defaultExpandTopBar") private var defaultExpandTopBar = true
    @State private var showTopBar: Bool
    @AppStorage("defaultView") private var defaultView: String = "all"
    @AppStorage("defaultViewMode") private var defaultViewMode = "daily"
    @State private var isLoading: Bool = false
    @State private var showCalendarTimeline = false
    @State private var selectedTimelineDate: Date = Date() // 新增
    @State private var isShowingSettings = false
    @State private var isShowingSearch = false
    @Namespace private var animation
    @State private var isDataLoaded = false
    @State private var isShowingStats = false
    @State private var isViewingAllData = true // 修改这里，将初始值设为 true
    @State private var scrollToBottom = false  // 新增：用于控制滚动到底部
    @State private var isInitialLoad = true  // 新增：用于标记初始加载
    @State private var selectedItemType: ItemType? // 新增：选中的项目类型
    @State private var loadTask: Task<Void, Never>?
    @State private var showLoadingIndicator = false
    @State private var selectedTags: Set<String> = [] // 改为 Set 支持多选
    @State private var viewMode: ViewMode = .normal // 新增
    @State private var debounceWorkItem: DispatchWorkItem? // 防抖处理
    
    let scrollToItem: ChatItem?
    
    // 添加滚动视图的引用
    @Namespace private var scrollSpace
    
    init(project: Project?, filter: ContentFilter = .project, scrollToItem: ChatItem?) {
        self.project = project
        self.filter = filter
        self.scrollToItem = scrollToItem
        _allItems = Query(sort: \ChatItem.timestamp, order: .reverse)
        // 使用 AppStorage 的值初始化 showTopBar
        _showTopBar = State(initialValue: UserDefaults.standard.bool(forKey: "defaultExpandTopBar"))
    }
    
    var body: some View {
        ScrollViewReader { proxy in
            VStack(spacing: 0) {
                // 使用 LazyVStack 延迟加载顶部栏
                if showTopBar {
                    TopBarView(
                        selectedFilter: $selectedFilter,
                        selectedDate: $selectedDate,
                        isShowingStats: $isShowingStats,
                        showCalendarTimeline: $showCalendarTimeline,
                        projectName: navigationTitle,
                        project: project,
                        isViewingAllData: $isViewingAllData,
                        selectedItemType: $selectedItemType,
                        selectedTags: $selectedTags,
                        chatItems: simplestChatViewModel.messages,
                        viewMode: $viewMode
                    )
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .zIndex(1)
                }
                
                // 优化内容视图
                ZStack(alignment: .top) {
                    if simplestChatViewModel.messages.isEmpty && isInitialLoad {
                        ProgressView()
                            .scaleEffect(1.5)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else {
                        contentView
                    }
                }
                .animation(.easeInOut, value: viewMode)
                
                // 使用 @ViewBuilder 优化输入区域
                inputArea
            }
            .navigationTitle(navigationTitle)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu {
                        Button(action: { isShowingSearch.toggle() }) {
                            Label("search".localized, systemImage: "magnifyingglass")
                        }
                        
                        // Menu {
                        //     Button {
                        //         selectedTags.removeAll()
                        //     } label: {
                        //         Label("全部标签", systemImage: "tag")
                        //     }
                            
                        //     if !availableTags.isEmpty {
                        //         Divider()
                                
                        //         ForEach(availableTags, id: \.self) { tag in
                        //             Button {
                        //                 if selectedTags.contains(tag) {
                        //                     selectedTags.remove(tag)
                        //                 } else {
                        //                     selectedTags.insert(tag)
                        //                 }
                        //             } label: {
                        //                 Label(tag, systemImage: selectedTags.contains(tag) ? "checkmark.circle.fill" : "circle")
                        //             }
                        //         }
                        //     }
                        // } label: {
                        //     Label("标签筛选", systemImage: selectedTags.isEmpty ? "tag" : "tag.fill")
                        // }
                        
                        Button(action: {
                            withAnimation(.spring()) {
                                showTopBar.toggle()
                            }
                        }) {
                            Label(showTopBar ? "hide_toolbar".localized : "show_toolbar".localized, systemImage: "rectangle.topthird.inset")
                        }
                        
                        Button(action: { isShowingSettings.toggle() }) {
                            Label("settings".localized, systemImage: "gear")
                        }
                        
                        Button(action: { isShowingStats = true }) {
                            Label("statistics".localized, systemImage: "chart.pie")
                        }
                        
                        Button(action: {
                            isViewingAllData.toggle()
                            loadChatItems()
                        }) {
                            Label(isViewingAllData ? "view_by_day".localized : "view_all".localized, systemImage: isViewingAllData ? "calendar" : "list.bullet")
                        }
                    } label: {
                        Image(systemName: "ellipsis")
                            .foregroundColor(.primary)
                    }
                }
            }
            .sheet(isPresented: $isShowingSettings) {
                SettingsView()
            }
            .sheet(isPresented: $isShowingSearch) {
                SearchView(chatItems: allItems)
            }
            .sheet(isPresented: $isShowingStats) {
                StatsView(selectedDate: $selectedDate, project: project)
            }
            .onAppear {
                print("🔍 [性能调试] ContentView onAppear - 过滤器: \(filter), 项目: \(project?.name ?? "无")")
                let appearStartTime = CFAbsoluteTimeGetCurrent()
                
                // setupNavigationBar()
                chatViewModel.setContext(modelContext)
                simplestChatViewModel.setContext(modelContext)
                loadChatItems()
                if filter == .project {
                    selectedFilter = .all
                } else {
                    selectedFilter = ItemFilter(rawValue: defaultView) ?? .all
                }
                showTopBar = defaultExpandTopBar
                isDataLoaded = true
                
                let appearEndTime = CFAbsoluteTimeGetCurrent()
                print("🔍 [性能调试] ContentView onAppear 完成, 耗时: \(String(format: "%.2f", (appearEndTime - appearStartTime) * 1000))ms")
            }
            .onDisappear {
                print("🔍 [性能调试] ContentView onDisappear - 清理资源")
                // 取消正在进行的任务
                loadTask?.cancel()
                debounceWorkItem?.cancel()
                // 清理视图模型状态以释放内存
                Task {
                    simplestChatViewModel.reset()
                }
            }
            .onChange(of: defaultExpandTopBar) { _, newValue in
                showTopBar = newValue
            }
            .onChange(of: selectedFilter) { oldValue, newValue in
                print(String(format: "filter_changed".localized, "\(oldValue)", "\(newValue)"))
                debouncedLoadChatItems()
                updateSelectedType(for: newValue)
            }
            .onChange(of: selectedDate) { _, _ in
                debouncedLoadChatItems()
            }
            .onChange(of: isViewingAllData) { _, _ in
                debouncedLoadChatItems()
            }
            .onChange(of: selectedItemType) { _, _ in
                debouncedLoadChatItems()
            }
            .onChange(of: filter) { _, _ in
                debouncedLoadChatItems()
            }
            .onChange(of: project) { _, _ in
                debouncedLoadChatItems()
            }
            .onChange(of: showCalendarTimeline) { _, _ in
                debouncedLoadChatItems()
            }
            // 添加通知监听器
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("NavigateToProject"))) { notification in
                if let project = notification.userInfo?["project"] as? Project {
                    presentationMode.wrappedValue.dismiss()
                    // 延迟执行以确保视图已经消失
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                        NotificationCenter.default.post(
                            name: NSNotification.Name("OpenProject"),
                            object: nil,
                            userInfo: ["project": project]
                        )
                    }
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("NavigateToUncategorized"))) { _ in
                presentationMode.wrappedValue.dismiss()
                // 延迟执行以确保视图已经消失
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    NotificationCenter.default.post(
                        name: NSNotification.Name("OpenUncategorized"),
                        object: nil
                    )
                }
            }
            .onChange(of: scrollToItem) { oldValue, newValue in
                if let item = newValue {
                    withAnimation {
                        proxy.scrollTo(item.id, anchor: .center)
                    }
                }
            }
        }
    }
    
    private var navigationTitle: String {
        switch filter {
        case .all:
            return "全部"
        case .uncategorized:
            return "未分类"
        case .project:
            return project?.name ?? "项目"
        case .images:
            return "图片"
        case .planned:
            return "计划"
        case .favorites:
            return "收藏"
        case .pomodoro:
            return "番茄钟"
        }
    }
    
    private var shouldShowBackButton: Bool {
        filter != .all && filter != .uncategorized
    }
    
    @MainActor
    private func loadChatItems() {
        let startTime = CFAbsoluteTimeGetCurrent()
        print("🔍 [性能调试] loadChatItems 开始 - 过滤器: \(filter), 项目: \(project?.name ?? "无")")
        
        // 取消之前的任务
        loadTask?.cancel()
        
        // 避免频繁更新UI
        let shouldShowLoading = chatItems.isEmpty
        if shouldShowLoading {
            isInitialLoad = true
        }
        
        loadTask = Task {
            // 使用本地变量捕获当前状态
            let currentFilter = filter
            let currentSelectedDate = selectedDate
            let currentSelectedItemType = selectedItemType
            let currentSelectedTags = selectedTags
            let currentIsViewingAllData = isViewingAllData
            
            let resetStartTime = CFAbsoluteTimeGetCurrent()
            // 使用极简的SimplestChatViewModel解决无限加载问题
            simplestChatViewModel.reset()
            let resetEndTime = CFAbsoluteTimeGetCurrent()
            print("🔍 [性能调试] reset 耗时: \(String(format: "%.2f", (resetEndTime - resetStartTime) * 1000))ms")
            
            let loadStartTime = CFAbsoluteTimeGetCurrent()
            await simplestChatViewModel.loadInitialMessages(
                project: project,
                filter: currentFilter,
                selectedDate: currentSelectedDate,
                selectedItemType: currentSelectedItemType,
                selectedTags: currentSelectedTags,
                isViewingAllData: currentIsViewingAllData,
                isPlannedView: currentFilter == .planned
            )
            let loadEndTime = CFAbsoluteTimeGetCurrent()
            print("🔍 [性能调试] loadInitialMessages 耗时: \(String(format: "%.2f", (loadEndTime - loadStartTime) * 1000))ms")
            
            await MainActor.run {
                isInitialLoad = false
                let totalTime = CFAbsoluteTimeGetCurrent() - startTime
                print("🔍 [性能调试] loadChatItems 总耗时: \(String(format: "%.2f", totalTime * 1000))ms")
            }
        }
    }
    
    // 防抖加载函数
    @MainActor
    private func debouncedLoadChatItems() {
        print("🔍 [性能调试] debouncedLoadChatItems 触发")
        debounceWorkItem?.cancel()
        debounceWorkItem = DispatchWorkItem {
            loadChatItems()
        }
        if let workItem = debounceWorkItem {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: workItem)
        }
    }
    
    // 优化过滤函数
    private func filterItems(
        filter: ContentFilter,
        selectedDate: Date,
        selectedItemType: ItemType?,
        selectedTags: Set<String>,
        isViewingAllData: Bool
    ) async -> [ChatItem] {
        await Task.detached(priority: .userInitiated) { [allItems] in
            allItems.filter { item in
                // 使用本地变量避免捕获self
                let matchesDate: Bool
                if filter == .planned {
                    if isViewingAllData {
                        matchesDate = item.plannedDate != nil
                    } else {
                        matchesDate = item.plannedDate != nil && 
                            Calendar.current.isDate(item.plannedDate!, inSameDayAs: selectedDate)
                    }
                } else {
                    matchesDate = isViewingAllData || 
                        Calendar.current.isDate(item.effectiveTimestamp, inSameDayAs: selectedDate)
                }
                
                let matchesFilter = filter.matches(item: item, project: project)
                let matchesItemType = selectedItemType == nil || item.type == selectedItemType
                let matchesTags = selectedTags.isEmpty || !Set(item.tags).isDisjoint(with: selectedTags)
                
                return matchesDate && matchesFilter && matchesItemType && matchesTags
            }
        }.value
    }
    
    // 优化排序函数
    private func sortItems(_ items: [ChatItem], filter: ContentFilter) async -> [ChatItem] {
        await Task.detached(priority: .userInitiated) {
            if filter == .planned {
                return items.sorted { 
                    let date1 = $0.plannedDate ?? $0.effectiveTimestamp
                    let date2 = $1.plannedDate ?? $1.effectiveTimestamp
                    return date1 < date2
                }
            } else {
                return items.sorted { $0.effectiveTimestamp > $1.effectiveTimestamp }
            }
        }.value
    }
    
    private func addItem() {
        // 不需要在这里设置 selectedProject，因为 InputAreaView 已经处理了这个逻辑
        Task {
            simplestChatViewModel.markNewMessageAdded()  // 标记新消息
            simplestChatViewModel.reset()
            await simplestChatViewModel.loadInitialMessages(
                project: project,
                filter: filter,
                selectedDate: selectedDate,
                selectedItemType: selectedItemType,
                selectedTags: selectedTags,
                isViewingAllData: isViewingAllData,
                isPlannedView: filter == .planned
            )
        }
    }
    
    private func deleteItem(_ item: ChatItem) {
        modelContext.delete(item)
        do {
            try modelContext.save()
            loadChatItems() // 重加载项目列表
        } catch {
                            print(String(format: "delete_project_error".localized, error.localizedDescription))
        }
    }
    
    private func updateSelectedType(for filter: ItemFilter) {
        switch filter {
        case .tasks:
            selectedType = .task
        case .notes:
            selectedType = .note
        case .all:
            // 保持当前选择或设置默认值
            break
        }
    }
    
    @MainActor
    private func updateItemPlannedDate(_ item: ChatItem, to newDate: Date?) {
        item.updatePlannedDate(newDate)
        do {
            try modelContext.save()
            loadChatItems() // 重新加载数据以更新视图
        } catch {
                            print(String(format: "update_planned_date_error".localized, error.localizedDescription))
        }
    }
    
    // 新增：计算可用标签的属性
    private var availableTags: [String] {
        let allTags = Set(allItems.flatMap { $0.tags })
        return Array(allTags).sorted()
    }
    
    // 使用 @ViewBuilder 分离内容视图
    @ViewBuilder
    private var contentView: some View {
        switch viewMode {
        case .normal:
            SimplestChatListView(
                items: $simplestChatViewModel.messages,
                onDelete: deleteItem,
                onLoadHistory: {
                    Task {
                        await simplestChatViewModel.loadHistoryMessages()
                    }
                },
                isViewingAllData: $isViewingAllData,
                selectedDate: selectedDate,
                isPlannedView: filter == .planned,
                scrollToItem: scrollToItem,
                isLoadingHistory: simplestChatViewModel.isLoadingHistory,
                hasMoreHistory: simplestChatViewModel.hasMoreHistory,
                isNewMessageAdded: simplestChatViewModel.isNewMessageAdded
            )
            .environment(\.isAllView, filter == .all)
        case .list:
            ListView(items: simplestChatViewModel.messages, onDelete: deleteItem)
        case .timeline:
            CalendarTimelineView(
                selectedDate: $selectedDate,
                chatItems: simplestChatViewModel.messages,
                filter: filter,
                project: project,
                selectedItemType: selectedItemType
            )
        case .diary:
            DiaryView(items: simplestChatViewModel.messages)
        }
    }
    
    // 使用 @ViewBuilder 分离输入区域
    @ViewBuilder
    private var inputArea: some View {
        InputAreaView(
            inputText: $inputText,
            inputAmount: $inputAmount,
            selectedType: $selectedType,
            pomodoroCount: $pomodoroCount,
            tags: $tags,
            selectedImages: $selectedImages,
            selectedProject: $selectedProject,
            selectedFilter: $selectedFilter,
            currentProject: project,
            onSend: addItem
        )
        .transition(.move(edge: .bottom))
    }
}
