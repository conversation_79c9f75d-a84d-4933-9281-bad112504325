import SwiftUI
import SwiftData

struct SearchView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    let chatItems: [ChatItem]
    
    var body: some View {
        NavigationView {
            List {
                ForEach(filteredItems) { item in
                    ChatItemRow(chatItem: item)
                }
            }
            .navigationTitle("search".localized)
            .navigationBarTitleDisplayMode(.inline)
            .searchable(text: $searchText, prompt: "搜索笔记和任务")
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var filteredItems: [ChatItem] {
        if searchText.isEmpty {
            return chatItems
        } else {
            return chatItems.filter { item in
                item.text.localizedCaseInsensitiveContains(searchText) ||
                item.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }
    }
}
