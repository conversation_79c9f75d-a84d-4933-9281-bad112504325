import SwiftUI
import SwiftData
import UserNotifications

@main
struct MyApp: App {
    @StateObject private var pomodoroManager = PomodoroManager()
    @StateObject private var themeManager = ThemeManager()
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    let modelContainer: ModelContainer
    
    init() {
        do {
            modelContainer = try ModelContainer(for: Project.self, ChatItem.self, ImageData.self)
            
            if !UserDefaults.standard.bool(forKey: "tutorialCreated") {
                TutorialData.createTutorialProject(modelContext: modelContainer.mainContext)
                UserDefaults.standard.set(true, forKey: "tutorialCreated")
            }
        } catch {
            fatalError("Could not initialize ModelContainer: \(error)")
        }
        
        requestNotificationPermission()
        
        // 订阅状态已在SubscriptionManager.init()中处理，这里只打印调试信息
        #if DEBUG
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            SubscriptionManager.shared.debugSubscriptionStatus()
        }
        #endif
    }
    
    var body: some Scene {
        WindowGroup {
            ContentViewAll()
                .environmentObject(themeManager)
                .environmentObject(pomodoroManager)
        }
        .modelContainer(modelContainer)
    }
}

struct ContentViewAll: View {
    @EnvironmentObject var themeManager: ThemeManager
    
    var body: some View {
        HomeView()
            .preferredColorScheme(themeManager.isDarkMode ? .dark : .light)
            .onChange(of: themeManager.themeMode) { oldValue, newValue in
                themeManager.updateTheme()
            }
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
                // 应用恢复前台时，仅在必要时同步订阅状态
                // 大部分情况下本地状态已经足够，避免频繁验证
                #if DEBUG
                print("应用恢复前台")
                #endif
            }
    }
}

func requestNotificationPermission() {
    UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
        if granted {
                            print("notification_permission_granted".localized)
        } else if let error = error {
                          print(String(format: "notification_permission_error".localized, error.localizedDescription))
        }
    }
}
