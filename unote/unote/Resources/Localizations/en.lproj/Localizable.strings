/* Common */
"app_name" = "UNote";
"save" = "Save";
"cancel" = "Cancel";
"delete" = "Delete";
"edit" = "Edit";
"done" = "Done";
"confirm" = "Confirm";
"back" = "Back";
"next" = "Next";
"previous" = "Previous";
"retry" = "Retry";
"loading" = "Loading...";
"processing" = "Processing...";
"continue" = "Continue";
"start" = "Start";
"end" = "End";
"none" = "None";
"all_day" = "All Day";
"set_start_time" = "Set Start Time";
"set_end_time" = "Set End Time";
"priority" = "Priority";
"repeat" = "Repeat";
"monthly" = "Monthly";
"yearly" = "Yearly";
"work" = "Work";
"study" = "Study";
"life" = "Life";
"entertainment" = "Entertainment";
"incomplete" = "Incomplete";
"status_format" = "Status: %@";
"details" = "Details";
"notes" = "Notes";
"mark_completed" = "Mark Complete";
"mark_incomplete" = "Mark Incomplete";
"morning" = "Morning";
"afternoon" = "Afternoon";
"save" = "Save";
"cancel" = "Cancel";
"share" = "Share";
"add" = "Add";
"save_to_album" = "Save to Album";
"add_new_type" = "Add New Type";
"new_type_name" = "New Type Name";
"enter_new_type_name" = "Please enter new type name";
"error" = "Error";
"success" = "Success";
"warning" = "Warning";
"info" = "Info";

/* Navigation */
"home" = "Home";
"settings" = "Settings";
"search" = "Search";
"profile" = "Profile";
"notifications" = "Notifications";

/* Chat & Items */
"input_placeholder" = "Enter content...";
"send_button" = "Send";
"note_type" = "Note";
"task_type" = "Task";
"expense_type" = "Expense";
"pomodoro_label" = "Pomodoro";
"add_note" = "Add Note";
"add_task" = "Add Task";
"add_expense" = "Add Expense";
"completed" = "Completed";
"pending" = "Pending";
"in_progress" = "In Progress";

/* Projects */
"project" = "Project";
"projects" = "Projects";
"create_project" = "Create Project";
"edit_project" = "Edit Project";
"delete_project" = "Delete Project";
"project_name" = "Project Name";
"project_description" = "Project Description";
"select_project" = "Select Project";

/* Statistics */
"statistics" = "Statistics";
"daily_stats" = "Daily Stats";
"weekly_stats" = "Weekly Stats";
"monthly_stats" = "Monthly Stats";
"total_tasks" = "Total Tasks";
"completed_tasks" = "Completed Tasks";
"efficiency" = "Efficiency";
"pomodoro_count" = "Pomodoro Count";

/* Settings */
"language" = "Language";
"theme" = "Theme";
"notifications_settings" = "Notification Settings";
"export" = "Export";
"import_data" = "Import Data";
"backup" = "Backup";
"restore" = "Restore";
"dark_mode" = "Dark Mode";
"light_mode" = "Light Mode";
"system_mode" = "System";
"system_language" = "System Language";

/* Errors */
"network_error" = "Network Error";
"data_error" = "Data Error";
"file_not_found" = "File Not Found";
"permission_denied" = "Permission Denied";
"unknown_error" = "Unknown Error";

/* Date & Time */
"today" = "Today";
"yesterday" = "Yesterday";
"tomorrow" = "Tomorrow";
"this_week" = "This Week";
"this_month" = "This Month";
"select_date" = "Select Date";
"select_time" = "Select Time";
"due_date" = "Due Date";
"start_date" = "Start Date";
"end_date" = "End Date";

/* Tags & Categories */
"tags" = "Tags";
"add_tag" = "Add Tag";
"remove_tag" = "Remove Tag";
"category" = "Category";
"categories" = "Categories";
"select_category" = "Select Category";

/* Subscription */
"subscribe" = "Subscribe";
"upgrade" = "Upgrade";
"premium" = "Premium";
"free_version" = "Free Version";
"subscription_expired" = "Subscription Expired";

/* Main Interface */
"mine" = "Mine";
"all" = "All";
"favorites" = "Favorites";
"favorite" = "Favorite";
"unfavorite" = "Unfavorite";
"images" = "Images";
"planned" = "Planned";
"set_plan" = "Set Plan";
"uncategorized" = "Uncategorized";

/* Input Related */
"input_placeholder" = "Enter content...";
"add_tag_placeholder" = "Add tag";
"amount_placeholder" = "Amount";
"pomodoro_stepper" = "%d 🍅";

/* Accounting Related */
"expense" = "Expense";
"income" = "Income";
"accounting_info" = "Accounting Info";
"currency_symbol" = "$";

/* Pomodoro Related */
"pomodoro_planned" = "Planned: %d";
"pomodoro_completed" = "Completed: %d";
"pomodoro_section" = "Pomodoro";

/* Project Related */
"no_project" = "None";
"project_section" = "Project";
"no_messages_yet" = "No messages yet";

/* Statistics Charts */
"project_progress" = "Project Progress";
"completion_trend" = "Completion Trend";
"time_distribution" = "Time Distribution";
"pomodoro_efficiency" = "Pomodoro Efficiency";
"tag_usage_stats" = "Tag Usage Statistics";
"chart_in_development" = "Chart in development...";

/* Upgrade Prompts */
"upgrade_to_premium" = "Upgrade to Premium";
"unlock_all_features" = "Unlock All Features";
"unlock_history" = "Unlock All History";
"unlock_editing" = "Unlock Editing Features";
"free_limit_message" = "Free version allows up to 3 projects";
"view_only" = "View Only";

/* Language Settings */
"language_selection" = "Language Selection";
"chinese_simplified" = "简体中文";
"english" = "English";
"japanese" = "日本語";
"language_change_notice" = "Language changes take effect immediately";

/* Settings Page */
"subscription_status" = "Subscription Status";
"subscription_status_subscribed" = "Subscribed";
"subscription_status_unlock" = "Unlock all premium features";
"lifetime_member_status" = "Lifetime Member";
"subscription_expires_on" = "Subscription expires: %@";
"free_member_status" = "Free User";
"theme_mode" = "Theme Mode";
"chat_interface" = "Chat Interface";
"reverse_alignment" = "Reverse task/note alignment";
"default_expand_toolbar" = "Default expand toolbar";
"bubble_font_size" = "Bubble font size";
"font_size_small" = "Small";
"font_size_medium" = "Medium";
"font_size_large" = "Large";
"pomodoro_settings" = "Pomodoro";
"work_duration" = "Work duration: %d minutes";
"break_duration" = "Break duration: %d minutes";
"data_management" = "Data Management";
"export_data" = "Export Data";
"about" = "About";
"version" = "Version";
"unknown_version" = "Unknown";
"rate_us" = "Rate Us";
"terms_of_service" = "Terms of Service";
"privacy_policy" = "Privacy Policy";
"technical_support" = "Technical Support";
"icp_record" = "粤ICP备2024331696号-2A";

/* Date Time Formats */
"today_format" = "Today HH:mm";
"yesterday_format" = "Yesterday HH:mm";
"this_year_format" = "MMM dd HH:mm";
"other_year_format" = "yyyy MMM dd HH:mm";
"date_format_simple" = "yyyy MMM dd";

/* Task Status */
"mark_completed" = "Complete";
"mark_incomplete" = "Mark Incomplete";
"all_tasks_completed" = "Completed all %d tasks";
"partial_tasks_completed" = "Completed %d/%d tasks";

/* Notes Statistics */
"single_note_recorded" = "Recorded 1 note (%d words)";
"multiple_notes_recorded" = "Recorded %d notes (%d words total)";
"notes_count" = "%d notes";
"tasks_count" = "%d tasks";
"no_activities_today" = "No activities today";

/* Pomodoro Statistics */
"single_pomodoro_focused" = "Focused for 1 pomodoro today";
"multiple_pomodoro_focused" = "Focused for %d pomodoros today";

/* Favorites */
"favorite" = "Favorite";
"unfavorite" = "Unfavorite";

/* UI Elements */
"avatar_image" = "Image";

/* Status Related */
"not_started" = "Not Started";
"paused" = "Paused";
"never" = "Never";
"daily" = "Daily";
"weekly" = "Weekly";
"monthly" = "Monthly";
"yearly" = "Yearly";
"priority_none" = "None";
"priority_low" = "Low";
"priority_medium" = "Medium";
"priority_high" = "High";

/* Interface Controls */
"hide_toolbar" = "Hide Toolbar";
"show_toolbar" = "Show Toolbar";
"view_by_day" = "View by Day";
"view_all" = "View All";

/* Chart Titles */
"custom_date_range_stats" = "Custom Date Range Statistics";
"start_date_label" = "Start Date";
"end_date_label" = "End Date";
"stats_in_development" = "Statistics in development...";

/* Navigation Titles */
"select_project_title" = "Select Project";
"edit_item_title" = "Edit Schedule";
"manage_tags_title" = "Manage Tags";
"new_project_title" = "New Project";
"select_icon_title" = "Select Icon";
"quick_create_title" = "Quick Create";
"export_data_title" = "Export Data";

/* Project Management */
"project_info_section" = "Project Information";
"icon_option" = "Icon";
"image_option" = "Image";
"icon_selection_section" = "Icon Selection";
"no_image_selected" = "No Image Selected";
"delete_project_button" = "Delete Project";
"project_created_success" = "Project Created Successfully";
"cannot_undo_operation" = "This operation cannot be undone";
"confirm_delete_project" = "Are you sure you want to delete \"%@\"?";

/* Category Management */
"income_type_title" = "Income Type";
"expense_type_title" = "Expense Type";
"existing_types_section" = "Existing Types";
"type_name_placeholder" = "Type Name";
"edit_income_type_title" = "Edit Income Type";
"edit_expense_type_title" = "Edit Expense Type";
"type_name_empty_error" = "Type name cannot be empty";
"salary_category" = "Salary";
"investment_category" = "Investment";
"other_category" = "Other";
"food_category" = "Food";
"transport_category" = "Transport";
"shopping_category" = "Shopping";

/* Input Interface */
"edit_type_label" = "Edit Type";
"type_label" = "Type";
"custom_label" = "Custom";
"manage_tags_label" = "Manage Tags";
"no_content_to_send" = "No content to send";
"sending_in_progress" = "Sending in progress, ignoring duplicate operation";
"add_note_placeholder" = "Add note...";
"add_task_placeholder" = "Add task...";
"upgrade_to_pro_editing" = "Upgrade to Pro to edit more projects";
"upgrade_button" = "Upgrade";
"view_only_mode" = "View Only";
"upgrade_to_unlock_editing" = "Upgrade to Premium to unlock editing features";
"search_projects_placeholder" = "Search projects";

/* Subscription Features */
"free_version_includes" = "Free Version Includes";
"premium_version_includes" = "Premium Version Additional Features";
"unlimited_projects_title" = "Unlimited Projects";
"unlimited_projects_desc" = "Create unlimited projects and notes";
"all_view_modes_title" = "All View Modes";
"all_view_modes_desc" = "Support for diary, list, timeline and other view modes";
"data_export_title" = "Data Export";
"data_export_desc" = "Export to PDF, CSV and other formats";
"advanced_stats_title" = "Advanced Statistics (Coming Soon)";
"advanced_stats_desc" = "View detailed usage statistics and data analysis";
"cloud_sync_title" = "Cloud Sync (Coming Soon)";
"cloud_sync_desc" = "Automatic data sync, seamless multi-device usage";
"subscription_not_found" = "No valid subscription found";
"purchase_failed" = "Purchase failed: %@";
"monthly_subscription" = "Monthly Subscription";
"thank_you_subscription" = "Thank you for your subscription";
"unlocked_features" = "You have unlocked all premium features:";
"unote_pro" = "UNote. PRO";
"pro" = "PRO";
"unlock_all_features_desc" = "Unlock all premium features and all future features.";
"processing_subscription" = "Processing your subscription request...";
"purchase_cancelled" = "You have cancelled this purchase";
"subscription_success" = "Thank you for your subscription, all premium features unlocked";

/* Image Related */
"image_actions" = "Image Actions";
"confirm_delete_image" = "Confirm Delete";
"delete_image_message" = "Are you sure you want to delete this image?";
"image_count_format" = "%d/%d";

/* Statistics and Data */
"unlock_advanced_analytics" = "Unlock Advanced Analytics";
"upgrade_for_detailed_stats" = "Upgrade to Premium to view detailed statistics and analysis reports";
"completion_trend_analysis" = "Completion Trend Analysis";
"time_allocation_visualization" = "Time Allocation Visualization";
"tag_usage_statistics" = "Tag Usage Statistics";
"pomodoro_efficiency_analysis" = "Pomodoro Efficiency Analysis";
"project_progress_tracking" = "Project Progress Tracking";
"overview_section" = "Overview";
"tasks_section" = "Tasks";
"pomodoro_section_stats" = "Pomodoro";
"finance_section" = "Finance";
"notes_section" = "Notes";
"data_charts_section" = "Data Charts";
"task_pomodoro_completion" = "Task and Pomodoro Completion";
"pomodoro_trend" = "Pomodoro Trend";
"income_expense_situation" = "Income and Expense";
"no_stats_data" = "No statistics data";
"amount_format" = "Amount: $%.2f";
"creation_time_format" = "Created: %@";

/* Tag Management */
"add_new_tag_section" = "Add New Tag";
"add_button" = "Add";
"common_tags_section" = "Common Tags";
"usage_count_format" = "Usage count: %d";
"recent_tags_section" = "Recent Tags";

/* Time Related */
"dawn_period" = "Dawn";
"morning_period" = "Morning";
"noon_period" = "Noon";
"evening_period" = "Evening";
"late_night_period" = "Late Night";
"stop_pomodoro" = "Stop Pomodoro";
"start_pomodoro" = "Start Pomodoro";

/* Other Interface Elements */
"learn_more" = "Learn More";
"use_button" = "Use";
"custom_icon_section" = "Custom Icon";

/* Subscription Additional */
"yearly_subscription" = "Annual Subscription";
"lifetime_subscription" = "Lifetime";
"yearly_features" = "Includes all premium features\nCan cancel anytime";
"lifetime_features" = "Includes all premium features\nFree future updates\nOne-time payment, lifetime use";
"lifetime_features_short" = "One-time payment, lifetime access to all features";
"most_popular" = "Most Popular";
"limited_offer" = "Limited Offer";
"purchase_error" = "Purchase failed: %@";
"lifetime_status" = "Lifetime";
"subscription_expires" = "Subscription expires: %@";
"close_button" = "Close";
"terms_of_service" = "Terms of Service";
"privacy_policy" = "Privacy Policy";
"technical_support" = "Technical Support";
"restore_purchases" = "Restore Purchases";
"subscription_failed" = "Subscription Failed";
"retry_later" = "Please try again later";
"purchase_canceled_title" = "Purchase Canceled";
"subscription_success_title" = "Subscription Successful";
"start_using" = "Start Using";
"purchase_verification_failed" = "Purchase verification failed, please try again later";
"purchase_processing" = "Purchase is being processed, please check later";
"unknown_purchase_error" = "Unknown error occurred, please try again later";

/* TextField Placeholders */
"enter_new_tag" = "Enter new tag";
"enter_tags" = "Enter tags...";
"enter_comment" = "Comment content";
"enter_emoji" = "Enter emoji";
"enter_custom_icon" = "Enter custom icon";
"enter_new_message" = "Enter new message";
"enter_tag" = "Enter tag";
"amount_zero" = "0.00";
"url_placeholder" = "URL";

/* Edit Interface */
"comment_toggle" = "Comment";
"add_comment" = "Add comment";
"accounting_info_section" = "Accounting Info";
"income_type" = "Income";
"expense_type" = "Expense";
"amount_label" = "Amount";
"category_label" = "Category";
"please_select" = "Please select";
"set_reminder" = "Set reminder";
"delete_project_confirm" = "Delete Project";
"plan_format" = "Plan: %d";
"completed_format" = "Completed: %d";

/* Button Text Additional */
"add_tag_button" = "Add";
"select_icon_button" = "Select Icon";
"select_image_button" = "Select Image";
"complete_button" = "Complete";
"custom_date" = "Custom date...";
"morning_9am" = "Morning 9:00";
"noon_12pm" = "Noon 12:00";
"evening_6pm" = "Evening 6:00";
"custom_time" = "Custom time...";
"cancel_plan" = "Cancel plan";
"count_format" = "%d items";
"custom_option" = "Custom...";

/* Diary Interface */
"today_format_simple" = "Today";
"yesterday_format_simple" = "Yesterday";
"date_format_year" = "yyyy MMM dd";
"single_note_recorded_format" = "Recorded 1 note (%d words)";
"multiple_notes_recorded_format" = "Recorded %d notes (%d words total)";
"single_pomodoro_today" = "Focused for 1 pomodoro today";
"multiple_pomodoro_today" = "Focused for %d pomodoros today";
"income_today" = "Income $%.2f";
"expense_today" = "Expense $%.2f";
"balance_today" = "Balance $%.2f";
"today_prefix" = "Today";
"note_emoji" = "📝 Note";
"task_emoji" = "✓ Task";
"expense_emoji" = "💰 Bill";

/* Statistics Interface */
"unlock_advanced_data_analysis" = "Unlock Advanced Data Analysis";
"upgrade_for_stats_analysis" = "Upgrade to Premium to view detailed statistics and analysis reports";

/* Debug Info (keep in English) */
"model_context_not_set" = "ModelContext not set";
"get_projects_failed" = "Get projects failed: %@";
"add_project_failed" = "Add project failed: %@";
"save_failed" = "Save failed: %@";
"delete_failed" = "Delete failed: %@";

/* Date Formats */
"month_day_format" = "MMM dd";
"month_day_time_format" = "MMM dd HH:mm";

/* Additional strings from DiaryView and other files */
"today_diary" = "Today";
"yesterday_diary" = "Yesterday";
"single_note_with_words" = "Recorded one note (%d words)";
"multiple_notes_with_words" = "Recorded %d notes (total %d words)";
"single_pomodoro_today" = "Focused for 1 pomodoro today";
"multiple_pomodoro_today" = "Focused for %d pomodoros today";
"income_amount" = "Income $%.2f";
"expense_amount" = "Expense $%.2f";
"balance_amount" = "Balance $%.2f";
"today_financial" = "Today";
"comma_separator" = ", ";
"period_separator" = ".";

/* Time Periods */
"dawn_time" = "Dawn";
"early_morning_time" = "Early Morning";
"noon_time" = "Noon";
"evening_time" = "Evening";
"late_night_time" = "Late Night";

/* Content Type Emojis */
"note_emoji" = "📝 Notes";
"task_emoji" = "✓ Tasks";
"expense_emoji" = "💰 Expenses";

/* Statistics Related */
"task_title" = "Tasks";
"pomodoro_title" = "Pomodoro";
"note_title" = "Notes";
"completion_percentage" = "%.1f%%";
"pending_tasks" = "%d";
"total_focus_time" = "Total Focus Time";
"new_notes" = "New";
"total_words" = "Total Words";
"task_completion_rate" = "Tasks";
"pomodoro_completion_rate" = "Pomodoro";
"completion_rate_title" = "Completion Rate";
"incomplete_title" = "Incomplete";
"income_title" = "Income";
"expense_title" = "Expense";
"income_chart_label" = "Income";
"expense_chart_label" = "Expense";
"task_chart_label" = "Tasks";
"pomodoro_chart_label" = "Pomodoro";
"date_chart_label" = "Date";
"completed_count_label" = "Completed";
"category_chart_label" = "Category";
"amount_chart_label" = "Amount";

/* Time Ranges */
"today_range" = "Today";
"this_week_range" = "This Week";
"this_month_range" = "This Month";
"this_year_range" = "This Year";

/* Avatar Types */
"faces_avatar" = "Faces";
"nature_avatar" = "Nature";
"objects_avatar" = "Objects";
"symbols_avatar" = "Symbols";
"custom_avatar" = "Custom";

/* Error Messages and Prompts */
"project_name_exists" = "Project name already exists";
"project_name_empty" = "Project name cannot be empty";
"save_failed_error" = "Save failed: %@";
"picker_avatar_type" = "Avatar Type";
"picker_icon_category" = "Icon Category";

/* Dialogs and Confirmations */
"comment_actions" = "Comment Actions";
"confirm_delete_project_title" = "Are you sure you want to delete this project?";

/* Statistics Page Titles */
"stats_title" = "Statistics";
"overview_title" = "Overview";
"tasks_title" = "Tasks";
"notes_title" = "Notes";
"finance_title" = "Finance";
"charts_title" = "Data Charts";
"time_range_picker" = "Time Range";

/* Project Management */
"project_category_picker" = "Project Category";
"avatar_type_picker" = "Avatar Type";
"delete_project_title" = "Delete Project";

/* Debug and Error Messages */
"get_projects_failed" = "Failed to get projects: %@";
"add_project_failed" = "Failed to add project: %@";
"update_category_failed" = "Error updating category: %@";
"delete_project_failed" = "Error deleting project: %@";
"save_project_failed" = "Error saving project: %@";
"no_content_to_send_debug" = "No content to send";
"sending_in_progress_debug" = "Sending in progress, ignoring duplicate operation";
"created_chat_item" = "Created new ChatItem: %@";
"save_chat_item_error" = "Error saving new chat item: %@";
"save_chat_item_success" = "Successfully saved ChatItem to database";
"save_image_failed" = "Save failed: %@";
"project_name_too_long" = "Project name cannot exceed 50 characters";
"create_project_failed" = "Failed to create project: %@";

/* TopBarView View Modes */
"standard_view" = "Standard View";
"list_view" = "List View";
"timeline_view" = "Timeline";
"diary_view" = "Diary View";
"all_types" = "All Types";
"note_filter" = "Notes";
"task_filter" = "Tasks";
"expense_filter" = "Expenses";
"select_date" = "Select Date";

/* StatsView Additional Strings */
"stats_navigation_title" = "Statistics";
"no_stats_data_available" = "No statistics data available";
"amount_display" = "Amount: $%.2f";
"creation_time_display" = "Creation time: %@";

/* Export Related */
"export_format" = "Export Format";
"start_export" = "Start Export";
"export_failed" = "Export Failed";
"unable_to_get_data" = "Unable to get data";
"export_file_creation_failed" = "Export file creation failed";

/* Accounting Information */
"accounting_info_header" = "Accounting Info";
"income_expense_type" = "Income/Expense Type";
"income_tag" = "Income";
"expense_tag" = "Expense";

/* Subscription Detailed Information */
"lifetime_features_detailed" = "One-time payment, use all features forever";
"yearly_features_detailed" = "Includes all premium features\nCan cancel subscription anytime";
"yearly_features_updated" = "Includes all premium features\nEnjoy future feature updates";
"lifetime_features_full" = "Includes all premium features\nGet future updates for free\nOne-time payment, use forever";
"lifetime_version" = "Lifetime Version";
"subscription_expiry" = "Subscription expires: %@";
"purchase_verification_failed" = "Purchase verification failed, please try again later";
"subscription_not_found" = "No valid subscription found";
"purchase_cancelled" = "Purchase cancelled";
"purchase_processing" = "Purchase is being processed, please check later";
"unknown_purchase_error" = "Unknown error occurred, please try again later";

/* ChatItemView Interactive Elements */
"select_time" = "Select Time";
"select_date_calendar" = "Select Date";
"confirm_button" = "Confirm";
"switch_to_note" = "Switch to Note";
"switch_to_task" = "Switch to Task";
"mark_incomplete" = "Mark as Incomplete";
"mark_complete" = "Mark as Complete";
"stop_pomodoro" = "Stop Pomodoro";
"start_pomodoro" = "Start Pomodoro";
"set_pomodoro_count" = "Set Pomodoro Count";
"copy_content" = "Copy Content";
"set_pomodoro_title" = "Set Pomodoro Count";

/* Language Names */
"chinese_traditional" = "Traditional Chinese";

/* Missing Localization Keys */
"view_only_mode" = "View Only Mode";
"upgrade_to_unlock_editing" = "Upgrade to Unlock Editing";
"existing_types_section" = "Existing Types";
"type_name_empty_error" = "Type name cannot be empty";
"free_version_includes" = "Free Version Includes";
"premium_version_includes" = "Premium Version Includes";
"thank_you_subscription" = "Thank You for Subscribing!";
"unlocked_features" = "Unlocked Features";
"unlock_all_features_desc" = "Unlock all premium features";
"subscription_success" = "Subscription Successful";
"confirm_delete_image" = "Confirm Delete Image";
"delete_image_message" = "Are you sure you want to delete this image?";
"custom_date_range_stats" = "Custom Date Range Statistics";
"stats_in_development" = "Statistics in development...";
"learn_more" = "Learn More";
"project_created_success" = "Project Created Successfully";
"add_new_tag_section" = "Add New Tag";
"add_button" = "Add";
"common_tags_section" = "Common Tags";
"recent_tags_section" = "Recent Tags";
"project_info_section" = "Project Information";
"icon_option" = "Icon";
"avatar_image" = "Avatar Image";
"icon_selection_section" = "Icon Selection";
"no_image_selected" = "No Image Selected";
"cannot_undo_operation" = "This operation cannot be undone";
"quick_create_title" = "Quick Create";
"custom_icon_section" = "Custom Icon";
"use_button" = "Use";
"pomodoro_section_stats" = "Pomodoro Statistics";
"task_pomodoro_completion" = "Task & Pomodoro Completion";
"pomodoro_trend" = "Pomodoro Trend";
"income_expense_situation" = "Income & Expense Overview";
"add_tag_button" = "Add Tag";

/* Home Page Search and Navigation */
"search_placeholder" = "Search";
"all_items" = "All";
"planned_items" = "Planned";
"favorites_items" = "Favorites";

/* Greetings */
"late_night_greeting" = "Late Night";
"good_morning" = "Good Morning";
"good_forenoon" = "Good Morning";
"good_noon" = "Good Noon";
"good_afternoon" = "Good Afternoon";
"good_evening" = "Good Evening";
"good_night" = "Good Night";

/* Statistics Text */
"wrote_notes_format" = "Wrote %d notes";
"tasks_count_format" = "%d tasks";
"income_format" = "Income $%.2f";
"expense_format" = "Expense $%.2f";
"no_records_today" = "No records today.";

/* Export Related Supplements */
"csv_format" = "CSV Table";
"pdf_format" = "PDF Diary";
"csv_description" = "CSV format contains all data, suitable for backup and data analysis";
"pdf_description" = "PDF format presented as diary, suitable for reading and printing";

/* Reminder Settings */
"early_reminder" = "Early Reminder";
"at_start_time" = "At Start Time";
"5_minutes_before" = "5 minutes before";
"15_minutes_before" = "15 minutes before";
"30_minutes_before" = "30 minutes before";
"1_hour_before" = "1 hour before";
"1_day_before" = "1 day before";
"custom_time" = "Custom";
"custom_reminder_time" = "Custom Time";
"set_end_time" = "Set End Time";
"end_time" = "End";

/* Tags and Attachments */
"tags_section" = "Tags";
"add_tag_placeholder" = "Add tag";
"recent_tags" = "Recent";
"attachments" = "Attachments";

/* Accounting Types and Default Categories */
"expense_type_key" = "Expense";
"income_type_key" = "Income";
"other_expense" = "Other Expense";
"other_income" = "Other Income";

/* Creation and Deletion Related */
"creation_time" = "Creation Time";
"confirm_delete" = "Confirm Delete";
"confirm_delete_item" = "Are you sure you want to delete this item?";

/* Time Related */
"yesterday_time" = "Yesterday";

/* Korean Language Name */
"korean" = "한국어";

/* Additional Date Formats for different locales */
"month_day_format_localized" = "MMM dd";
"year_month_day_format" = "yyyy MMM dd";
"year_month_day_time_format" = "yyyy MMM dd HH:mm";
"weekday_format" = "EEEE";

/* Time Units */
"hours_unit" = "hours";
"minutes_unit" = "minutes";
"time_format" = "%d %@ %d %@";

/* Date Range Titles */
"date_range_today" = "Today";
"date_range_this_week" = "This Week";
"date_range_this_month" = "This Month";
"date_range_this_year" = "This Year";
"date_range_custom" = "Custom";

/* PDF Export */
"pdf_diary_title" = "My UNote Diary";
"pdf_diary_subtitle" = "Recording Life's Moments";
"pdf_export_date" = "Export Date:";
"pdf_table_of_contents" = "Table of Contents";

/* Custom Time and Editing */
"custom_display_time" = "Custom Display Time";
"display_time" = "Display Time";
"delete_future_tasks_error" = "Error deleting future recurring tasks: %@";

/* Debug Messages (keep in English for debugging) */
"model_context_not_set" = "ModelContext not set";
"csv_export_error" = "CSV export error: %@";
"pdf_export_error" = "PDF export error: %@";
"save_data_failed" = "Failed to save data: %@";
"filter_changed" = "Filter changed: from %@ to %@";
"delete_project_error" = "Error deleting project: %@";
"update_planned_date_error" = "Error updating planned date: %@";
"notification_permission_granted" = "Notification permission granted";
"notification_permission_error" = "Error requesting notification permission: %@";

/* Tutorial Manager - TutorialManager */
"tutorial_project_name" = "Welcome to uNote";
"tutorial_tag" = "Tutorial";

/* Tutorial Content - Welcome and Basic Input */
"tutorial_welcome_message" = "👋 Hello! Welcome to uNote!\n\nLet me help you quickly understand the main features of this app.";
"tutorial_basic_input" = "💡 The most basic way to use:\n\nJust type content in the input box below and send to create a note.\n\nGive it a try!";
"tutorial_types_switch" = "📝 uNote supports three types of content:\n\n• Notes - Record thoughts and information\n• Tasks - Todo management\n• Expenses - Income and expense records\n\nTap the icon on the left side of the input box to switch types.";

/* Tutorial Content - Content Editing and Organization */
"tutorial_tags_1" = "🏷️ Use tags to organize content:\n\nType # to quickly add tags, like #work #life";
"tutorial_tags_2" = "Advanced tag features:\n\n• Tap tags to filter related content\n• Long press tags to edit or delete\n• Manage common tags in settings";
"tutorial_image" = "📸 Add images:\n\n• Tap the camera icon above the input box\n• Support taking photos or selecting from album\n• Can add multiple images\n• Long press images to preview";
"tutorial_comment" = "💬 Comment feature:\n\n• Tap the comment icon on the right side of content\n• Can add supplementary notes\n• Support creating subtasks";

/* Tutorial Content - Task Management */
"tutorial_task_1" = "✅ Create tasks:\n\n• Tap the left side of input box to switch to task mode\n• Enter task content\n• Can set due date\n• Check off when completed";
"tutorial_task_2" = "Advanced task features:\n\n• Set priority\n• Add reminders\n• Recurring tasks\n• Location reminders";
"tutorial_pomodoro_1" = "🍅 Pomodoro Technique:\n\nWhen creating tasks, you can set the number of pomodoros to help you stay focused.";
"tutorial_pomodoro_2" = "Using pomodoro timer:\n\n• Tap start focus\n• 25 minutes work\n• 5 minutes break\n• Track focus time";

/* Tutorial Content - Accounting Features */
"tutorial_expense_1" = "💰 Record expenses:\n\nTap the card icon above the input box, enter amount and select category.";
"tutorial_expense_2" = "Accounting features:\n\n• Support income and expenses\n• Custom expense categories\n• View expense statistics\n• Budget management";

/* Tutorial Content - View and Analysis */
"tutorial_view_mode_1" = "👀 Four view modes:\n\nTap the icon in the upper right to switch views:\n\n📱 Standard view\n📋 List view\n⏰ Timeline\n📔 Diary view";
"tutorial_view_mode_2" = "Choose the right view:\n\n• Standard view - Daily recording\n• List view - Task management\n• Timeline - Review history\n• Diary view - Writing diary";
"tutorial_stats" = "📊 Data statistics:\n\n• Task completion status\n• Focus time analysis\n• Expense trends\n• Tag usage frequency";

/* Tutorial Content - Project Management */
"tutorial_project_1" = "📁 Use project management:\n\n• Create different projects\n• Set project icons\n• Move content to projects\n• Switch between projects";
"tutorial_project_2" = "🎉 Tutorial complete!\n\nNow you understand the main features of uNote.\n\nStart creating your own projects!";

/* Tutorial Data Projects - TutorialData */
"tutorial_data_project_name" = "Tutorial";
"diary_project_name" = "Diary";

/* Diary Project Content */
"diary_welcome_title" = "📔 Welcome to your diary";
"diary_welcome_comment_1" = "This is your private space";
"diary_welcome_comment_2" = "Record daily moods and insights";
"diary_welcome_comment_3" = "Let words capture life's beauty";
"diary_suggestions_title" = "Some suggestions for diary writing:";
"diary_suggestion_1" = "🌅 Record the most special moments of the day";
"diary_suggestion_2" = "💭 Share inner thoughts and feelings";
"diary_suggestion_3" = "🎯 Write down recent goals and plans";
"diary_suggestion_4" = "Be grateful for the beautiful things in life 🍀";

/* Tutorial Data Content */
"tutorial_data_accounting_title" = "💰 Accounting Feature";
"tutorial_data_accounting_comment_1" = "Easily record daily expenses";
"tutorial_data_accounting_comment_2" = "Clearly understand income and expenses";
"tutorial_data_accounting_comment_3" = "Develop good financial habits";

"tutorial_data_shortcuts_title" = "👆 Quick Operations";
"tutorial_data_shortcuts_comment_1" = "Make every operation more natural";
"tutorial_data_shortcuts_comment_2" = "Reduce unnecessary clicks";
"tutorial_data_shortcuts_comment_3" = "Quickly complete daily recording";
"tutorial_data_shortcuts_1" = "Tap content: View details/edit more";
"tutorial_data_shortcuts_2" = "Long press content: Open more options";
"tutorial_data_shortcuts_3" = "Top search: Enter keywords to find";

"tutorial_data_content_org_title" = "Flexible content organization";
"tutorial_data_content_org_comment_1" = "Make information more organized";
"tutorial_data_content_org_comment_2" = "Easy to find and review";
"tutorial_data_content_org_comment_3" = "Adapt to different usage scenarios";
"tutorial_data_content_org_1" = "# Add tags: Type # then enter tag, or tap tag icon";
"tutorial_data_content_org_2" = "@ Select project: Type @ then select, or tap project icon";
"tutorial_data_content_org_3" = "Favorite content: Long press then tap favorite";
"tutorial_data_content_org_4" = "Copy content: Long press then tap copy";

"tutorial_data_view_mode_title" = "👀 Viewing methods";
"tutorial_data_view_mode_comment_1" = "Different perspectives on content";
"tutorial_data_view_mode_comment_2" = "Adapt to different usage scenarios";
"tutorial_data_view_mode_comment_3" = "Make information more valuable";
"tutorial_data_view_mode_1" = "Chat view: Tap chat icon";
"tutorial_data_view_mode_2" = "List view: Tap list icon";
"tutorial_data_view_mode_3" = "Diary view: Tap diary icon";
"tutorial_data_view_mode_4" = "Timeline view: Tap calendar icon";
"tutorial_data_view_mode_5" = "View switching: Top toolbar switching";

"tutorial_data_focus_title" = "Focus and efficiency";
"tutorial_data_focus_comment_1" = "Let tools serve life better";
"tutorial_data_focus_comment_2" = "Improve time utilization efficiency";
"tutorial_data_focus_comment_3" = "Develop good habits";
"tutorial_data_focus_1" = "🍅 Pomodoro: Long press task - start pomodoro";
"tutorial_data_focus_2" = "Pomodoro operation: Long press task content, tap start pomodoro";
"tutorial_data_focus_3" = "Full screen mode: After starting pomodoro, tap full screen icon in bubble";
"tutorial_data_focus_4" = "Focus duration: 25 minutes work + 5 minutes break";
"tutorial_data_focus_5" = "Plan tasks: Long press task to set pomodoro count";
"tutorial_data_focus_6" = "Pomodoro reminder: Notification when work/break time is up";

"tutorial_data_tips_title" = "✨ Usage suggestions";
"tutorial_data_tips_comment_1" = "Recording creates value";
"tutorial_data_tips_comment_2" = "Make life more organized";
"tutorial_data_tips_comment_3" = "Looking forward to your story";
"tutorial_data_tips_1" = "Record inspiration: Enter and record anytime";
"tutorial_data_tips_2" = "Organize and categorize: Use tags and projects";
"tutorial_data_tips_3" = "Develop habits: Regular review and organization";
"tutorial_data_tips_4" = "Record beauty: Make life more organized";

"tutorial_data_project_mgmt_title" = "📁 Project Management";
"tutorial_data_project_mgmt_comment_1" = "Categorize and organize your content";
"tutorial_data_project_mgmt_comment_2" = "Make records more organized";
"tutorial_data_project_mgmt_comment_3" = "Adapt to different scenario needs";
"tutorial_data_project_mgmt_1" = "Create project: Tap plus icon in top left of home page - new project";
"tutorial_data_project_mgmt_2" = "Edit project: Long press project - edit";
"tutorial_data_project_mgmt_3" = "Project category examples:";
"tutorial_data_project_mgmt_4" = "Study Notes";
"tutorial_data_project_mgmt_5" = "Work Items";
"tutorial_data_project_mgmt_6" = "Life Records";
"tutorial_data_project_mgmt_7" = "Financial Management";
"tutorial_data_project_mgmt_8" = "Personal Growth";

"tutorial_data_ending_title" = "💌 Message";
"tutorial_data_ending_comment_1" = "Thank you for using UNote";
"tutorial_data_ending_comment_2" = "May it become your helpful assistant";
"tutorial_data_ending_comment_3" = "Let's record life's beauty together";
"tutorial_data_ending_1" = "Please start viewing tutorial from the top";
"tutorial_data_ending_2" = "Every record is a mark of life";
"tutorial_data_ending_3" = "Every day deserves to be treated gently";

/* Demo Data Projects - DemoData */
"demo_project_work" = "Work";
"demo_project_life" = "Life";
"demo_project_health" = "Health";

/* Demo Data Content - Morning Health */
"demo_morning_run" = "Start morning run 🏃";
"demo_run_record" = "Ran 5.2 kilometers today 🎯";
"demo_run_pace" = "Pace 5'23\", a bit faster than last week 💪";
"demo_run_idea" = "Thought of an idea while running: listen to podcasts while running 💡";
"demo_breakfast" = "Breakfast";
"demo_breakfast_comment" = "The meat filling at this bun shop is really delicious, will come again next time 😋";

/* Demo Data Content - Work */
"demo_new_day" = "Start a new day ☀️";
"demo_design_review" = "Prepare product design review 📊";
"demo_user_feedback" = "User feedback: interface needs to be more intuitive 📝";
"demo_design_idea" = "Just thought of using card-style design 💭";
"demo_interaction_idea" = "Inspiration: swipe left to quickly complete tasks ✨";
"demo_sort_question" = "Should todo items support drag and drop sorting? 🤔";
"demo_team_discuss" = "Remember to ask others for their thoughts 📝";

/* Demo Data Content - Noon */
"demo_lunch" = "Lunch";
"demo_lunch_comment" = "The braised pork at this restaurant is good 😋";
"demo_nap" = "Nap time 😴";
"demo_reading" = "Saw a good article before sleep, remember to finish reading later 📚";

/* Demo Data Content - Afternoon Work */
"demo_modify_design" = "Modify design proposal ✏️";
"demo_dark_mode_idea" = "Suddenly thought: should we add dark mode? 🌙";
"demo_weather_widget" = "Could add a weather widget to the homepage ☀️";
"demo_theme_idea" = "Supporting custom theme colors would be better 🎨";
"demo_animation_idea" = "Remember to research animation effects ✨";
"demo_stationery" = "Stationery";
"demo_pen_comment" = "This pen writes very comfortably, will recommend to colleagues ✍️";

/* Demo Data Content - Evening Life */
"demo_shopping" = "Go grocery shopping 🛒";
"demo_fruits" = "Fruits";
"demo_strawberry_comment" = "Strawberries this season are especially sweet 🍓";
"demo_vegetables" = "Vegetables";
"demo_broccoli_comment" = "Today's broccoli looks very fresh 🥦";
"demo_bread_milk" = "Bread and milk";
"demo_bread_comment" = "The whole wheat bread from this bakery is good 🍞";
"demo_yoga" = "Practice yoga 🧘‍♀️";
"demo_yoga_comment" = "Found a nice yoga video, bookmarked it 📺";

/* Demo Data Content - Evening */
"demo_cook_dinner" = "Cook dinner 👨‍🍳";
"demo_cooking_comment" = "Tried a new dish today, tastes pretty good 👍";
"demo_read_book" = "Reading time 📖";
"demo_book_comment" = "Today's book is very inspiring 💡";
"demo_plan_tomorrow" = "Plan tomorrow's schedule 📅";
"demo_plan_comment" = "Need to prepare PPT for tomorrow's meeting 📊";

/* Demo Data Categories */
"demo_category_dining" = "Dining";
"demo_category_office" = "Office";
"demo_category_daily" = "Daily";
"demo_category_example" = "Example";

/* 恢复购买相关 */
"restore_failed" = "Restore Failed";
"restore_failed_message" = "No previous purchases found to restore. Please confirm if you have made any purchases before.";
"restore_success" = "Restore Successful";
"restore_success_message" = "Your purchase has been successfully restored and subscription features are now active.";

/* New Project Related */
"project_name_placeholder" = "Enter project name";
"creating_project" = "Creating...";
"quick_create_subtitle" = "Choose preset templates to quickly create projects";
"quick_create_description" = "Select a preset template to quickly create a project and start recording";
"create" = "Create";
"tap_to_change" = "Tap to change";
"search_icons" = "Search icons";