# UNote 项目重构计划
*遵循"less is more"原则的科学重构方案*

## 🎯 重构目标

### 核心原则
- **简化复杂性**：减少文件层级，提高可读性
- **模块化设计**：清晰的功能边界，易于维护
- **国际化支持**：完整的多语言体系
- **技术债务清理**：消除冗余代码，提升性能

## 📁 极简项目结构

```
UNote/
├── App/                         # 应用入口
│   └── UNoteApp.swift          # 主应用文件 (合并所有启动逻辑)
├── Models/                      # 数据模型 (简化的核心数据)
│   ├── Project.swift
│   ├── ChatItem.swift
│   └── AppState.swift          # 统一状态管理
├── Views/                       # 视图层 (按功能分组)
│   ├── Home/
│   ├── Chat/
│   ├── Settings/
│   └── Shared/                 # 共享组件
├── Managers/                    # 业务逻辑管理器
│   ├── DataManager.swift       # 统一数据管理
│   ├── LocalizationManager.swift
│   └── ThemeManager.swift
├── Extensions/                  # 扩展和工具
│   ├── String+Localization.swift
│   ├── View+Extensions.swift
│   └── Color+Theme.swift
└── Resources/                   # 资源文件
    ├── Assets.xcassets/
    └── Localizations/
        ├── zh-Hans.lproj/
        ├── en.lproj/
        └── ja.lproj/
```

**极简原则**：
- 最多3层目录深度
- 每个目录职责单一且清晰
- 减少文件数量，合并相似功能
- 统一管理器模式，避免过度抽象

## 🌍 极简多语言支持

### 单一本地化文件结构
```
Resources/Localizations/
├── zh-Hans.lproj/Localizable.strings    # 简体中文 (默认)
├── en.lproj/Localizable.strings         # 英文
└── ja.lproj/Localizable.strings         # 日文
```

### 统一管理器
- `LocalizationManager`: 一个文件解决所有本地化需求
- 类型安全的字符串键枚举
- SwiftUI直接集成

## 🏗️ 极简架构原则

### 1. 单层架构
- **Views**: 直接使用SwiftData，无需额外抽象
- **Managers**: 最少必要的业务逻辑管理器
- **Models**: 纯数据模型，无业务逻辑

### 2. 直接依赖
- 避免过度抽象和Protocol
- 直接使用具体类型
- 简单的Singleton模式

### 3. 合并相似功能
- 一个文件处理一类功能
- 减少文件跳转
- 降低认知负担

## 📝 极简实施步骤

### ✅ Step 1: 基础架构搭建 (已完成)
1. ✅ 创建统一状态管理器 (`AppState.swift`)
2. ✅ 创建统一数据管理器 (`UnifiedDataManager.swift`) 
3. ✅ 建立多语言支持系统 (`LocalizationManager.swift`)
4. ✅ 创建本地化字符串文件 (中/英/日三种语言)
5. ✅ 创建本地化扩展工具 (`String+Localization.swift`)

### 🔄 Step 2: 目录重组与文件迁移 (进行中)
1. 🔄 按新目录结构移动现有文件
2. ⏳ 合并功能相似的小文件
3. ⏳ 拆分超大文件 (如515行的ContentView.swift)

### ⏳ Step 3: 代码重构与优化 (待进行)
1. ⏳ 替换硬编码字符串为本地化版本
2. ⏳ 整合现有的Managers到统一架构
3. ⏳ 简化视图层级，减少文件数量

### ⏳ Step 4: 最终清理 (待进行)
1. ⏳ 删除冗余代码和未使用文件
2. ⏳ 统一代码风格和命名规范
3. ⏳ 性能优化和测试

## 🎯 下一步行动

**立即可执行**：
1. 重新组织现有文件到新目录结构
2. 拆分大型ContentView.swift文件
3. 更新主应用文件使用新的管理器

## 🎨 极简设计系统

### 统一主题管理器
- 一个`ThemeManager`处理所有主题
- 最少必要的颜色(5-8个核心色)
- 系统字体为主，减少自定义字体
- 自动深色/浅色模式切换

### 基础组件
- 标准SwiftUI组件优先
- 仅创建真正需要的自定义组件
- 组件复用率>80%

## 📊 极简成功指标

- [ ] 文件数量减少50%
- [ ] 代码行数减少40%
- [ ] 支持3种核心语言(中/英/日)
- [ ] 构建时间减少30%
- [ ] 文件深度≤3层

## 🚀 维护原则

1. **一个文件一个职责**
2. **优先合并而非拆分**  
3. **删除胜过添加**
4. **简单胜过复杂**

---

*此重构计划遵循"less is more"原则，旨在创建简洁、高效、可维护的代码架构。* 