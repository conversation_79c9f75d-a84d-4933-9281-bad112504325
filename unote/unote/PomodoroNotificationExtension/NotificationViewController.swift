import UIKit
import UserNotifications
import UserNotificationsUI

class NotificationViewController: UIViewController, UNNotificationContentExtension {
    @IBOutlet var titleLabel: UILabel?
    @IBOutlet var bodyLabel: UILabel?
    @IBOutlet var progressView: UIProgressView?

    func didReceive(_ notification: UNNotification) {
        titleLabel?.text = notification.request.content.title
        bodyLabel?.text = notification.request.content.body
        
        if let userInfo = notification.request.content.userInfo as? [String: Any],
           let progress = userInfo["progress"] as? Double {
            progressView?.progress = Float(progress)
        }
    }
}
