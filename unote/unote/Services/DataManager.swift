import SwiftData
import Foundation // 添加这行

class DataManager {
    private var context: ModelContext?

    func setContext(_ context: ModelContext) {
        self.context = context
    }

    @MainActor
    func fetchItems() async -> [ChatItem] {
        guard let context = context else {
            print("model_context_not_set".localized)
            return []
        }
        do {
            let descriptor = FetchDescriptor<ChatItem>(sortBy: [SortDescriptor(\.timestamp, order: .reverse)])
            return try context.fetch(descriptor)
        } catch {
                            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return []
        }
    }
    
    @MainActor
    func fetchLatestItems(limit: Int = 20) async -> [ChatItem] {
        guard let context = context else {
            print("model_context_not_set".localized)
            return []
        }
        do {
            // 获取最新的数据，然后按时间正序排列（最新的在底部）
            var descriptor = FetchDescriptor<ChatItem>(sortBy: [SortDescriptor(\.timestamp, order: .reverse)])
            descriptor.fetchLimit = limit
            let items = try context.fetch(descriptor)
            // 反转顺序，使最新的消息在底部
            return items.reversed()
        } catch {
            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return []
        }
    }
    
    @MainActor
    func fetchItemsWithPagination(offset: Int = 0, limit: Int = 20) async -> [ChatItem] {
        guard let context = context else {
            print("model_context_not_set".localized)
            return []
        }
        do {
            // 获取更早的消息（向前分页）
            var descriptor = FetchDescriptor<ChatItem>(sortBy: [SortDescriptor(\.timestamp, order: .reverse)])
            descriptor.fetchOffset = offset
            descriptor.fetchLimit = limit
            let items = try context.fetch(descriptor)
            // 反转顺序，使时间早的在前面，新的在后面
            return items.reversed()
        } catch {
            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return []
        }
    }
    
    @MainActor
    func fetchItemsWithFilter(
        project: Project? = nil,
        filter: ContentFilter? = nil,
        selectedDate: Date? = nil,
        selectedItemType: ItemType? = nil,
        selectedTags: Set<String> = [],
        isViewingAllData: Bool = true,
        isPlannedView: Bool = false,
        offset: Int = 0,
        limit: Int = 20
    ) async -> [ChatItem] {
        let startTime = CFAbsoluteTimeGetCurrent()
        print("🔍 [数据层性能] fetchItemsWithFilter 开始 - offset: \(offset), limit: \(limit)")
        
        guard let context = context else {
            print("model_context_not_set".localized)
            return []
        }
        
        // 对于简单情况，使用数据库层面的分页
        if filter == .all && selectedItemType == nil && selectedTags.isEmpty && isViewingAllData {
            let result = await fetchItemsWithPagination(offset: offset, limit: limit)
            let totalTime = CFAbsoluteTimeGetCurrent() - startTime
            print("🔍 [数据层性能] 简单分页查询耗时: \(String(format: "%.2f", totalTime * 1000))ms, 结果: \(result.count)条")
            return result
        }
        
        do {
            // 对于复杂过滤，使用较小的缓冲区大小以降低内存使用
            let estimatedFetchSize = max(Double(limit) * 1.5, 50).rounded() // 进一步减少倍数以降低内存使用
            
            var descriptor = FetchDescriptor<ChatItem>(sortBy: [SortDescriptor(\.timestamp, order: .reverse)])
            descriptor.fetchOffset = offset
            descriptor.fetchLimit = Int(estimatedFetchSize)
            
            let fetchStartTime = CFAbsoluteTimeGetCurrent()
            let allItems = try context.fetch(descriptor)
            let fetchEndTime = CFAbsoluteTimeGetCurrent()
            print("🔍 [数据层性能] SwiftData fetch 耗时: \(String(format: "%.2f", (fetchEndTime - fetchStartTime) * 1000))ms, 获取: \(allItems.count)条")
            
            // 在内存中应用过滤条件
            let filterStartTime = CFAbsoluteTimeGetCurrent()
            let filteredItems = allItems.filter { item in
                // ContentFilter 过滤
                if let contentFilter = filter {
                    switch contentFilter {
                    case .all:
                        break
                    case .uncategorized:
                        if item.project != nil { return false }
                    case .project:
                        if let project = project {
                            if item.project?.id != project.id { return false }
                        }
                    case .images:
                        if item.imageData.isEmpty { return false }
                    case .planned:
                        if item.plannedDate == nil { return false }
                    case .favorites:
                        if !item.isFavorite { return false }
                    case .pomodoro:
                        if (item.pomodoroCount ?? 0) == 0 && item.completedPomodoros == 0 { return false }
                    }
                }
                
                // 类型过滤
                if let itemType = selectedItemType {
                    if item.type != itemType { return false }
                }
                
                // 日期过滤
                if !isViewingAllData, let selectedDate = selectedDate {
                    let calendar = Calendar.current
                    let startOfDay = calendar.startOfDay(for: selectedDate)
                    let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
                    
                    if isPlannedView {
                        guard let plannedDate = item.plannedDate else { return false }
                        if plannedDate < startOfDay || plannedDate >= endOfDay { return false }
                    } else {
                        if item.effectiveTimestamp < startOfDay || item.effectiveTimestamp >= endOfDay { return false }
                    }
                }
                
                // 标签过滤
                if !selectedTags.isEmpty {
                    let itemTagsSet = Set(item.tags)
                    if itemTagsSet.isDisjoint(with: selectedTags) { return false }
                }
                
                return true
            }
            let filterEndTime = CFAbsoluteTimeGetCurrent()
            print("🔍 [数据层性能] 内存过滤耗时: \(String(format: "%.2f", (filterEndTime - filterStartTime) * 1000))ms, 过滤后: \(filteredItems.count)条")
            
            // 应用分页
            let startIndex = offset
            let endIndex = min(startIndex + limit, filteredItems.count)
            
            if startIndex >= filteredItems.count {
                let totalTime = CFAbsoluteTimeGetCurrent() - startTime
                print("🔍 [数据层性能] 无数据返回, 总耗时: \(String(format: "%.2f", totalTime * 1000))ms")
                return []
            }
            
            // 反转结果，使最新的在底部
            let result = Array(filteredItems[startIndex..<endIndex].reversed())
            let totalTime = CFAbsoluteTimeGetCurrent() - startTime
            print("🔍 [数据层性能] fetchItemsWithFilter 完成, 总耗时: \(String(format: "%.2f", totalTime * 1000))ms, 返回: \(result.count)条")
            return result
        } catch {
            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return []
        }
    }

    // MARK: - 新的基于时间戳的分页方法
    @MainActor
    func fetchItemsBefore(date: Date, limit: Int = 20) async -> [ChatItem] {
        guard let context = context else {
            print("model_context_not_set".localized)
            return []
        }
        
        do {
            var descriptor = FetchDescriptor<ChatItem>(
                predicate: #Predicate { $0.timestamp < date },
                sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
            )
            descriptor.fetchLimit = limit
            let items = try context.fetch(descriptor)
            
            // 返回时间顺序正确的结果（早到晚）
            return items.reversed()
        } catch {
            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return []
        }
    }
    
    @MainActor
    func fetchItemsWithFilterAndDate(
        project: Project? = nil,
        filter: ContentFilter? = nil,
        selectedDate: Date? = nil,
        selectedItemType: ItemType? = nil,
        selectedTags: Set<String> = [],
        isViewingAllData: Bool = true,
        isPlannedView: Bool = false,
        beforeDate: Date? = nil,
        limit: Int = 20
    ) async -> [ChatItem] {
        guard let context = context else {
            print("model_context_not_set".localized)
            return []
        }
        
        do {
            // 基础时间筛选
            var timePredicates: [Foundation.Predicate<ChatItem>] = []
            
            if let beforeDate = beforeDate {
                timePredicates.append(#Predicate { $0.timestamp < beforeDate })
            }
            
            var descriptor = FetchDescriptor<ChatItem>(sortBy: [SortDescriptor(\.timestamp, order: .reverse)])
            descriptor.fetchLimit = limit * 3 // 获取更多数据用于过滤
            
            // 应用时间筛选
            if !timePredicates.isEmpty {
                descriptor.predicate = timePredicates.first
            }
            
            let items = try context.fetch(descriptor)
            
            // 在内存中应用其他过滤条件
            let filteredItems = items.filter { item in
                // ContentFilter 过滤
                if let contentFilter = filter {
                    switch contentFilter {
                    case .all:
                        break
                    case .uncategorized:
                        if item.project != nil { return false }
                    case .project:
                        if let project = project {
                            if item.project?.id != project.id { return false }
                        }
                    case .images:
                        if item.imageData.isEmpty { return false }
                    case .planned:
                        if item.plannedDate == nil { return false }
                    case .favorites:
                        if !item.isFavorite { return false }
                    case .pomodoro:
                        if (item.pomodoroCount ?? 0) == 0 && item.completedPomodoros == 0 { return false }
                    }
                }
                
                // 类型过滤
                if let itemType = selectedItemType {
                    if item.type != itemType { return false }
                }
                
                // 日期过滤
                if !isViewingAllData, let selectedDate = selectedDate {
                    let calendar = Calendar.current
                    let startOfDay = calendar.startOfDay(for: selectedDate)
                    let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay)!
                    
                    if isPlannedView {
                        guard let plannedDate = item.plannedDate else { return false }
                        if plannedDate < startOfDay || plannedDate >= endOfDay { return false }
                    } else {
                        if item.effectiveTimestamp < startOfDay || item.effectiveTimestamp >= endOfDay { return false }
                    }
                }
                
                // 标签过滤
                if !selectedTags.isEmpty {
                    let itemTagsSet = Set(item.tags)
                    if itemTagsSet.isDisjoint(with: selectedTags) { return false }
                }
                
                return true
            }
            
            // 只取需要的数量，保持时间顺序
            let limitedItems = Array(filteredItems.prefix(limit))
            return limitedItems.reversed() // 早到晚的顺序
            
        } catch {
            print(String(format: "get_projects_failed".localized, error.localizedDescription))
            return []
        }
    }

    @MainActor
    func addItem(_ item: ChatItem) async {
        guard let context = context else {
            print("model_context_not_set".localized)
            return
        }
        do {
            context.insert(item)
            try context.save()
        } catch {
                            print(String(format: "add_project_failed".localized, error.localizedDescription))
        }
    }
}
