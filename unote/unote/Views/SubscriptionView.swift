import SwiftUI

// MARK: - 主订阅视图
struct SubscriptionView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var viewModel = SubscriptionViewModel()
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    var body: some View {
        NavigationView {
            if subscriptionManager.isSubscribed {
                SubscriptionSuccessView(dismiss: dismiss)
            } else {
                SubscriptionPurchaseView(viewModel: viewModel, dismiss: dismiss)
            }
        }
        .onAppear {
            viewModel.dismissAction = {
                dismiss()
            }
        }
        .alert("error_title".localized, isPresented: $viewModel.showError) {
            Button("ok_button".localized) { }
        } message: {
            Text(viewModel.errorMessage ?? "")
        }
    }
}

// MARK: - 订阅成功视图
struct SubscriptionSuccessView: View {
    let dismiss: DismissAction
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showRestoreAlert = false
    @State private var showRestoreSuccess = false
    
    var body: some View {
        VStack(spacing: 24) {
            // 订阅成功动画
            SubscriptionSuccessAnimationView()
                .padding(.top, 40)
            
            Text("thank_you_subscription".localized)
                .font(.title2)
                .fontWeight(.bold)
            
            VStack(alignment: .leading, spacing: 16) {
                Text(subscriptionManager.getSubscriptionStatusText())
                    .font(.subheadline)
                
                SubscribedFeatureListView()
            }
            
            Spacer()
            
            // 管理选项
            VStack(spacing: 12) {
                Button("restore_purchases".localized) {
                    Task {
                        do {
                            try await subscriptionManager.restorePurchases()
                            showRestoreSuccess = true
                        } catch {
                            showRestoreAlert = true
                        }
                    }
                }
                .buttonStyle(.bordered)
                
                Button("close_button".localized) {
                    dismiss()
                }
                .buttonStyle(.borderedProminent)
            }
            .padding(.bottom, 20)
        }
        .padding()
        .navigationBarTitleDisplayMode(.inline)
        .alert("restore_failed".localized, isPresented: $showRestoreAlert) {
            Button("ok_button".localized) { }
        } message: {
            Text("restore_failed_message".localized)
        }
        .alert("restore_success".localized, isPresented: $showRestoreSuccess) {
            Button("ok_button".localized) { }
        } message: {
            Text("restore_success_message".localized)
        }
    }
}

// MARK: - 订阅购买视图
struct SubscriptionPurchaseView: View {
    @Bindable var viewModel: SubscriptionViewModel
    let dismiss: DismissAction
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        ZStack(alignment: .bottom) {
            ScrollView {
                VStack(alignment: .leading, spacing: 24) {
                    headerSection
                    
                    SubscriptionFeatureView(
                        features: SubscriptionFeature.allFeatures,
                        isSubscribed: false
                    )
                    
                    SubscriptionProductGrid(
                        products: viewModel.products,
                        selectedProduct: $viewModel.selectedProduct
                    )
                    
                    subscriptionDescriptionSection
                    
                    footerLinksSection
                    
                    Spacer(minLength: 64)
                }
            }
            .disabled(viewModel.isPurchasing)
            
            purchaseButton
        }
        .background(BackgroundColor.background)
        .navigationBarTitleDisplayMode(.inline)
        .overlay {
            if viewModel.isPurchasing {
                purchaseOverlay
            }
        }
    }
    
    // MARK: - 头部区域
    private var headerSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("unote_pro".localized)
                .font(.system(size: 20, weight: .bold))
                .padding(.horizontal, 6)
                .padding(.vertical, 2)
                .background(SubscriptionStyle.primaryColor)
                .cornerRadius(6)
                .foregroundStyle(.white)
            
            Text("unlock_all_features_desc".localized)
                .font(.system(size: 14))
        }
        .padding(.top, 48)
        .padding(.horizontal, 24)
    }
    
    // MARK: - 订阅说明区域
    private var subscriptionDescriptionSection: some View {
        Group {
            if !viewModel.getSubscriptionDescription().isEmpty {
                Text(viewModel.getSubscriptionDescription())
                    .font(.system(size: 13))
                    .foregroundStyle(.secondary)
                    .padding(.horizontal, 20)
                    .padding(.top, 4)
            }
        }
    }
    
    // MARK: - 底部链接区域
    private var footerLinksSection: some View {
        HStack(spacing: 8) {
            Link("terms_of_service".localized, destination: URL(string: "http://www.unote.site/terms")!)
            Link("privacy_policy".localized, destination: URL(string: "http://www.unote.site/privacy")!)
            Link("technical_support".localized, destination: URL(string: "http://www.unote.site/support")!)
            Button("restore_purchases".localized) {
                Task {
                    await viewModel.restorePurchases()
                }
            }
            Spacer()
        }
        .font(.footnote)
        .foregroundStyle(.secondary)
        .padding(.horizontal, 20)
        .padding(.top, 16)
    }
    
    // MARK: - 购买按钮
    private var purchaseButton: some View {
        VStack(spacing: 0) {
            Divider()
                .opacity(0.5)
            
            Button {
                Task {
                    await viewModel.purchase()
                }
            } label: {
                HStack {
                    if viewModel.isPurchasing {
                        ProgressView()
                            .tint(.white)
                            .padding(.trailing, 8)
                    }
                    
                    Text(viewModel.isPurchasing ? "processing".localized : "continue".localized)
                        .font(.system(size: 17, weight: .semibold))
                }
                .frame(maxWidth: .infinity)
                .frame(height: 50)
                .background(viewModel.isPurchasing ? Color(.systemGray2) : SubscriptionStyle.primaryColor)
                .foregroundStyle(.white)
                .cornerRadius(12)
            }
            .padding(.horizontal)
            .padding(.vertical, 12)
            .disabled(viewModel.selectedProduct == nil || viewModel.isPurchasing)
        }
        .background(BackgroundColor.background)
        .disabled(viewModel.isPurchasing)
    }
    
    // MARK: - 购买遮罩层
    private var purchaseOverlay: some View {
        ZStack {
            Color.black.opacity(0.2)
                .ignoresSafeArea()
            
            VStack(spacing: 12) {
                ProgressView()
                    .scaleEffect(1.2)
                    .tint(.white)
                
                Text("processing_subscription".localized)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
            }
            .padding(24)
            .background(Color.black.opacity(0.8))
            .cornerRadius(12)
        }
    }
}
