import SwiftUI

struct AdvancedStatsView: View {
    @EnvironmentObject var subscriptionManager: SubscriptionManager
    
    var body: some View {
        Group {
            if subscriptionManager.isSubscribed {
                ScrollView {
                    VStack(spacing: 20) {
                        CompletionTrendChart()
                        TimeDistributionChart()
                        TagUsageChart()
                        PomodoroEfficiencyChart()
                        ProjectProgressChart()
                        CustomDateRangeStats()
                    }
                    .padding()
                }
            } else {
                UpgradePromptView(
                                title: "unlock_advanced_data_analysis".localized,
            description: "upgrade_for_stats_analysis".localized,
            features: [
                "completion_trend_analysis".localized,
                "time_allocation_visualization".localized,
                "tag_usage_statistics".localized,
                "pomodoro_efficiency_analysis".localized,
                "project_progress_tracking".localized
                    ]
                )
            }
        }
    }
}
