import SwiftUI
import Charts

struct TagUsageChart: View {
    var body: some View {
        VStack(alignment: .leading) {
            Text("tag_usage_stats".localized)
                .font(.headline)
            
            // 这里添加具体的图表实现
            Text("chart_in_development".localized)
                .foregroundColor(.secondary)
        }
        .padding()
        .background(Color.secondary.opacity(0.1))
        .cornerRadius(10)
    }
}
