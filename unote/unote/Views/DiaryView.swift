import SwiftUI

extension Sequence where Element: Hashable {
    func unique() -> [Element] {
        var seen = Set<Element>()
        return filter { seen.insert($0).inserted }
    }
}

struct DiaryView: View {
    let items: [ChatItem]
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showSubscription = false
    
    // 添加可见项目的过滤逻辑
    private var visibleItems: [ChatItem] {
        if subscriptionManager.isSubscribed {
            return items
        } else {
            // 非会员可以查看最近3天的内容（从最新的记录开始算）
            let sortedDates = items.map { $0.effectiveTimestamp }
                .map { Calendar.current.startOfDay(for: $0) }
                .unique()
                .sorted(by: >)
            
            guard let latestDate = sortedDates.first,
                  let threeDaysAgo = Calendar.current.date(byAdding: .day, value: -2, to: latestDate) else {
                return items
            }
            
            return items.filter { item in
                let itemDay = Calendar.current.startOfDay(for: item.effectiveTimestamp)
                return itemDay >= threeDaysAgo
            }
        }
    }
    
    private var groupedItems: [Date: [ChatItem]] {
        Dictionary(grouping: visibleItems) { item in
            Calendar.current.startOfDay(for: item.effectiveTimestamp)
        }
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(alignment: .leading, spacing: 48) {
                ForEach(groupedItems.keys.sorted(by: >), id: \.self) { day in
                    if let dayItems = groupedItems[day] {
                        VStack(alignment: .leading, spacing: 20) {
                            // 日期标题区域
                            VStack(alignment: .leading, spacing: 4) {
                                Text(formatDay(day))
                                    .font(.title2.bold())
                                
                                Text(formatWeekday(day))
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                                    // .padding(.bottom, 2)
                                
                                // Divider()
                                //     .padding(.top, 8)
                            }
                            .padding(.horizontal, 20)
                            
                            // 今日总结区域
                            VStack(alignment: .leading, spacing: 8) {
                                let summary = generateDailySummary(for: dayItems)
                                if !summary.isEmpty {
                                    Text(summary)
                                        .font(.system(size: 14))
                                        .foregroundStyle(.secondary)
                                        .lineSpacing(6)
                                        .padding(.vertical, 14)
                                        .padding(.horizontal, 16)
                                        .frame(maxWidth: .infinity, alignment: .leading)
                                        .background(Color(.systemGray6).opacity(0.5))
                                        .clipShape(RoundedRectangle(cornerRadius: 8))
                                }
                                
                            }
                            .padding(.horizontal, 20)
                            .padding(.bottom, 4)
                            
                            // DashedLine()
                            //     .padding(.horizontal, 16)
                            // 详细内容区域
                            VStack(alignment: .leading, spacing: 16) {
                                ForEach(groupByTypeAndTime(dayItems), id: \.0) { type, timeGroups in
                                    VStack(alignment: .leading, spacing: 16) {
                                        // 类型标题
                                        Text(type)
                                            .font(.headline)
                                            // .padding(.top, 8)
                                        
                                        // 按时间段显示内容
                                        ForEach(timeGroups, id: \.0) { timeOfDay, items in
                                            VStack(alignment: .leading, spacing: 6) {
                                                // 添加时间段标题
                                                // Text(timeOfDay)
                                                //     .font(.caption)
                                                //     .opacity(0.3)
                                                    // .foregroundColor(.secondary)
                                                    // .padding(.bottom, 4)
                                                
                                                ForEach(items) { item in
                                                    let isFirstInHour = items.first { 
                                                        Calendar.current.component(.hour, from: $0.timestamp) == 
                                                        Calendar.current.component(.hour, from: item.timestamp)
                                                    } == item
                                                    
                                                    DiaryContentView(item: item, showTime: isFirstInHour)
                                                        .padding(.top, isFirstInHour && items.firstIndex(of: item) != 0 ? 24 : 0)
                                                }
                                            }
                                            .padding(.bottom, 8)
                                        }
                                    }
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                    
                                    // 在每个类型后添加分隔线，最后一个除外
                                    if type != groupByTypeAndTime(dayItems).last?.0 {
                                        // 简化的虚线分隔线
                                        
                                        DashedLine()
                                    }
                                }
                            }
                            .frame(maxWidth: .infinity, alignment: .leading)
                            .padding(.horizontal, 20)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.vertical, 32)
                        .frame(maxWidth: 795)
                        .background(Color(uiColor: .systemBackground))
                        // .overlay(
                        //     Rectangle()
                        //         .stroke(Color(uiColor: .separator), lineWidth: 0.5)
                        // )
                    }

                    // Divider()
                    //     .opacity(0.5)
                }
                
                // 添加订阅提示
                if !subscriptionManager.isSubscribed && !items.isEmpty {
                    HStack(spacing: 16) {
                        VStack(alignment: .leading, spacing: 6) {
                            Text("upgrade_to_premium".localized)
                                .font(.headline)
                                .foregroundColor(.primary)
                            Text("unlock_history".localized)
                                .font(.subheadline)
                                .foregroundColor(.secondary)
                        }
                        
                        Spacer()
                        
                        Image(systemName: "wand.and.sparkles.inverse")
                            .font(.title2)
                            .foregroundStyle(.primary)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 16)
                            .fill(Color(uiColor: .systemBackground))
                            .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 2)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 16)
                            .stroke(Color.primary.opacity(0.1), lineWidth: 1)
                    )
                    .padding(.horizontal, 16)
                    .padding(.vertical, 32)
                    .onTapGesture {
                        showSubscription = true
                    }
                }
            }
            
            // .padding(.vertical, 16)
        }
        .sheet(isPresented: $showSubscription) {
            SubscriptionView()
        }
        // .background(Color(uiColor: .systemGroupedBackground))
    }
    
    private func formatDay(_ date: Date) -> String {
        if Calendar.current.isDateInToday(date) {
            return "today_diary".localized
        } else if Calendar.current.isDateInYesterday(date) {
            return "yesterday_diary".localized
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "year_month_day_format".localized
            return formatter.string(from: date)
        }
    }
    
    private func generateDailySummary(for items: [ChatItem]) -> String {
        // 任务统计
        let allTasks = items.filter { $0.type == .task }
        let completedTasks = allTasks.filter { $0.completed }
        let taskSummary = if !allTasks.isEmpty {
            if completedTasks.count == allTasks.count {
                                        String(format: "all_tasks_completed".localized, allTasks.count)
            } else {
                                    String(format: "partial_tasks_completed".localized, completedTasks.count, allTasks.count)
            }
        } else {
            ""
        }
        
        // 笔记统计
        let notes = items.filter { $0.type == .note }
        let totalWords = notes.reduce(0) { $0 + $1.text.count }
        let noteSummary = if !notes.isEmpty {
            if notes.count == 1 {
                String(format: "single_note_with_words".localized, totalWords)
            } else {
                String(format: "multiple_notes_with_words".localized, notes.count, totalWords)
            }
        } else {
            ""
        }
        
        // 番茄钟统计
        let pomodoros = items.filter { $0.text.contains("🍅") }.count
        let pomodoroSummary = switch pomodoros {
            case 0: ""
            case 1: "single_pomodoro_today".localized
            default: String(format: "multiple_pomodoro_today".localized, pomodoros)
        }
        
        // 账单统计
        let expenses = items.filter { $0.type == .expense }
        let income = expenses.filter { $0.amount ?? 0 > 0 }.reduce(0) { $0 + ($1.amount ?? 0) }
        let expenditure = abs(expenses.filter { $0.amount ?? 0 < 0 }.reduce(0) { $0 + ($1.amount ?? 0) })
        
        // 账单统计优化
        let financeSummary: String = {
            if income == 0 && expenditure == 0 { return "" }
            
            var parts: [String] = []
            if income > 0 { parts.append(String(format: "income_amount".localized, income)) }
            if expenditure > 0 { parts.append(String(format: "expense_amount".localized, expenditure)) }
            if income > expenditure {
                parts.append(String(format: "balance_amount".localized, income - expenditure))
            }
            return "today_financial".localized + parts.joined(separator: "comma_separator".localized)
        }()
        
        // 分两行组织内容
        let firstLine = [noteSummary, taskSummary]
            .filter { !$0.isEmpty }
            .joined(separator: "，")
        
        let secondLine = [pomodoroSummary, financeSummary]
            .filter { !$0.isEmpty }
            .joined(separator: "，")
        
        // 组合最终内容
        return [firstLine, secondLine]
            .filter { !$0.isEmpty }
            .joined(separator: "period_separator".localized + "\n")
            .appending(secondLine.isEmpty ? "period_separator".localized : "period_separator".localized)
    }
    
    private func groupByTypeAndTime(_ items: [ChatItem]) -> [(String, [(String, [ChatItem])])] {
        let timeSlots = [
            (0..<6, "dawn_time".localized),
            (6..<9, "early_morning_time".localized),
            (9..<12, "morning".localized),
            (12..<14, "noon_time".localized),
            (14..<18, "afternoon".localized),
            (18..<22, "evening_time".localized),
            (22..<24, "late_night_time".localized)
        ]
        
        // 首先按类型分组
        let typeGroups = Dictionary(grouping: items) { item in
            switch item.type {
            case .note: return "note_emoji".localized
            case .task: return "task_emoji".localized
            case .expense: return "expense_emoji".localized
            }
        }
        
        // 自定义类型排序顺序
        let typeOrder = ["note_emoji".localized, "task_emoji".localized, "expense_emoji".localized]
        
        // 然后对每个类型内的内容按时间段分组
        return typeGroups.map { type, typeItems in
            var timeGroupedItems: [String: [ChatItem]] = [:]
            
            // 按时间段分组，并在每组内按小时进一步分组
            for item in typeItems {
                let hour = Calendar.current.component(.hour, from: item.effectiveTimestamp)
                if let timeSlot = timeSlots.first(where: { $0.0.contains(hour) }) {
                    timeGroupedItems[timeSlot.1, default: []].append(item)
                }
            }
            
            // 将时间段排序并转换为元组数组，同时确保每组内的项目按时间排序
            let sortedTimeGroups = timeSlots.compactMap { slot -> (String, [ChatItem])? in
                guard let items = timeGroupedItems[slot.1], !items.isEmpty else { return nil }
                
                // 按小时分组并排序
                let sortedItems = items.sorted(by: { $0.effectiveTimestamp < $1.effectiveTimestamp })
                return (slot.1, sortedItems)
            }
            
            return (type, sortedTimeGroups)
        }
        .sorted { first, second in
            let firstIndex = typeOrder.firstIndex(of: first.0) ?? Int.max
            let secondIndex = typeOrder.firstIndex(of: second.0) ?? Int.max
            return firstIndex < secondIndex
        }
    }
    
    // 新增星期格式化函数
    private func formatWeekday(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "weekday_format".localized
        return formatter.string(from: date)
    }
    
    // 新增日期数字格式化函数
    private func formatDayNumber(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "dd"
        return formatter.string(from: date)
    }
}

// 简化DiaryContentView的样式
struct DiaryContentView: View {
    let item: ChatItem
    let showTime: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(alignment: .top, spacing: 8) {
                // 使用固定宽度的时间占位符
                Group {
                    if showTime {
                        Text(formatTime(item.effectiveTimestamp))
                            .font(.caption)
                            .opacity(0.3)
                    } else {
                        Text("")
                    }
                }
                .padding(.top, 2)
                .frame(width: 36, alignment: .leading) // 固定宽度确保对齐

                VStack(alignment: .leading, spacing: 8) {
                    // 文本内容、标签和金额在同一行
                    HStack(spacing: 4) {
                        (Text(item.text)
                            .font(.system(size: 15))
                            .strikethrough(item.type == .task && item.completed) +
                            
                        Text(buildTagsText())
                            .font(.system(size: 15))
                            .foregroundStyle(.primary.opacity(0.5)))
                            .lineSpacing(2)
                        if let amount = item.amount {
                            Text("\(amount > 0 ? "+" : "-")¥\(String(format: "%.2f", abs(amount)))")
                                .font(.system(size: 15))
                                .padding(.leading, 4)
                                // .foregroundColor(.secondary)
                        }
                    }
                    
                    // 横向滚动图片
                    if !item.imageData.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(item.imageData, id: \.self) { data in
                                    if let uiImage = UIImage(data: data) {
                                        Image(uiImage: uiImage)
                                            .resizable()
                                            .aspectRatio(contentMode: .fit)
                                            .frame(height: 120)
                                            .clipShape(RoundedRectangle(cornerRadius: 8))
                                            .background(
                                                RoundedRectangle(cornerRadius: 8)
                                                    .fill(Color(uiColor: .systemGray6))
                                            )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    private func buildTagsText() -> String {
        item.tags.isEmpty ? "" : " " + item.tags.map { "#\($0)" }.joined(separator: " ")
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
}

// 为内容添加更多纸质感的修饰器
struct PaperStyleModifier: ViewModifier {
    @Environment(\.colorScheme) private var colorScheme // 获取当前颜色方案
    
    func body(content: Content) -> some View {
        content
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(uiColor: .systemBackground))
                    .shadow(
                        color: colorScheme == .dark 
                            ? Color.black.opacity(0.2) 
                            : Color.black.opacity(0.03),
                        radius: colorScheme == .dark ? 4 : 3,
                        x: 0,
                        y: colorScheme == .dark ? 2 : 1
                    )
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        colorScheme == .dark 
                            ? Color.white.opacity(0.1) 
                            : Color.secondary.opacity(0.1),
                        lineWidth: 0.5
                    )
            )
    }
}

// 使用示例
extension View {
    func paperStyle() -> some View {
        modifier(PaperStyleModifier())
    }
}

// 添加简单的虚线视图
struct DashedLine: View {
    var body: some View {
        GeometryReader { geometry in
            Path { path in
                path.move(to: CGPoint(x: 0, y: 0))
                path.addLine(to: CGPoint(x: geometry.size.width, y: 0))
            }
            .stroke(style: StrokeStyle(lineWidth: 1, dash: [2, 2]))
            .foregroundColor(Color(uiColor: .separator).opacity(0.5))
        }
    }
} 
