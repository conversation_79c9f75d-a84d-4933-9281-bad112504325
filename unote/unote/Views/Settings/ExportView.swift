import SwiftUI
import SwiftData
import UIKit

struct ExportView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @State private var exportType = "csv"
    @State private var isExporting = false
    @State private var exportURL: URL?
    @State private var showShareSheet = false
    @State private var showError = false
    @State private var errorMessage = ""
    
    var body: some View {
        NavigationView {
            Form {
                Section {
                    Picker("export_format".localized, selection: $exportType) {
                        Text("csv_format".localized).tag("csv")
                        Text("pdf_format".localized).tag("pdf")
                    }
                    .pickerStyle(.segmented)
                }
                
                Section {
                    Button(action: exportData) {
                        HStack {
                            Text("start_export".localized)
                            if isExporting {
                                Spacer()
                                ProgressView()
                            }
                        }
                    }
                    .disabled(isExporting)
                }
                
                Section {
                    Text("csv_description".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text("pdf_description".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .navigationTitle("export_data_title".localized)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showShareSheet) {
            if let url = exportURL {
                FileShareView(items: [url])
            }
        }
        .alert("export_failed".localized, isPresented: $showError) {
                            Button("confirm".localized, role: .cancel) { }
        } message: {
            Text(errorMessage)
        }
    }
    
    private func exportData() {
        isExporting = true
        
        // 获取所有数据
        let descriptor = FetchDescriptor<ChatItem>()
        guard let items = try? modelContext.fetch(descriptor) else {
            showError(message: "unable_to_get_data".localized)
            return
        }
        
        // 根据选择的格式导出
        let url = if exportType == "csv" {
            ExportManager.shared.exportToCSV(items: items)
        } else {
            ExportManager.shared.exportToPDF(items: items)
        }
        
        if let url = url {
            exportURL = url
            showShareSheet = true
        } else {
            showError(message: "export_file_creation_failed".localized)
        }
        
        isExporting = false
    }
    
    private func showError(message: String) {
        errorMessage = message
        showError = true
        isExporting = false
    }
}

// 重命名为 FileShareView 以避免冲突
struct FileShareView: UIViewControllerRepresentable {
    let items: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        UIActivityViewController(
            activityItems: items,
            applicationActivities: nil
        )
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
} 