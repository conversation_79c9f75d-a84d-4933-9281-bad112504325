import SwiftUI
import SwiftData

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.colorScheme) var colorScheme
    @AppStorage("defaultPomodoroWorkTime") private var defaultPomodoroWorkTime = 25
    @AppStorage("defaultPomodoroBreakTime") private var defaultPomodoroBreakTime = 5
    @AppStorage("defaultExpandTopBar") private var defaultExpandTopBar = true
    @AppStorage("reverseAlignment") private var reverseAlignment = false
    @AppStorage("bubbleFontSize") private var bubbleFontSize: Double = 16
    @AppStorage("defaultViewMode") private var defaultViewMode = "daily"
    @Environment(\.modelContext) private var modelContext
    @State private var showingExportSheet = false
    @State private var showSubscriptionView = false
    @ObservedObject private var subscriptionManager = SubscriptionManager.shared
    @ObservedObject private var localizationManager = LocalizationManager.shared
    
    // 字体大小选项
    enum FontSize: Double, CaseIterable, Identifiable {
        case small = 15
        case medium = 16
        case large = 18
        
        var id: Double { self.rawValue }
        
        var description: String {
            switch self {
            case .small: return "font_size_small".localized
            case .medium: return "font_size_medium".localized
            case .large: return "font_size_large".localized
            }
        }
    }
    
    var body: some View {
        NavigationView {
            List {
                // 订阅卡片
                subscriptionCard
                
                // 主题设置
                themeSection
                
                // 语言设置
                languageSection
                
                // 聊天界面设置
                chatInterfaceSection
                
                // 番茄钟设置
                pomodoroSection
                
                // 数据管理
                dataManagementSection
                
                // 关于
                aboutSection
            }
            .navigationTitle("settings".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized) {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingExportSheet) {
            ExportView()
        }
        .sheet(isPresented: $showSubscriptionView) {
            SubscriptionView()
        }
        .preferredColorScheme(themeManager.themeMode == .system ? nil : (themeManager.isDarkMode ? .dark : .light))
    }
    
    // 订阅卡片 - 简化设计，专注于状态显示
    // 设计要点：
    // 1. 左侧图标区域：48x48，带背景色和边框
    // 2. 中间内容区域：标题+状态描述，带PRO徽章
    // 3. 右侧状态指示器：简洁的状态图标
    // 4. 移除操作按钮，操作移至订阅详情页
    private var subscriptionCard: some View {
        Section {
            HStack(spacing: 12) {
                // 左侧图标
                Image(systemName: subscriptionManager.isSubscribed ? "crown.fill" : "crown")
                    .font(.system(size: 20))
                    .fontWeight(.medium)
                    .foregroundColor(subscriptionManager.isSubscribed ? .yellow : .primary.opacity(0.85))
                    .frame(width: 48, height: 48)
                    .background(Color(.theme0).opacity(colorScheme == .dark ? 1 : 0.03))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .inset(by: 0.50)
                            .stroke(Color(.text0).opacity(0.05), lineWidth: 0.50)
                    )
                
                VStack(alignment: .leading, spacing: 6) {
                    // 标题行
                    HStack(spacing: 4) {
                        Text("subscription_status".localized)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(.label))
                        
                        if subscriptionManager.isSubscribed {
                            Text("pro".localized)
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 6)
                                .padding(.vertical, 2)
                                .background(Color.yellow)
                                .cornerRadius(4)
                        }
                    }
                    
                    Text(subscriptionManager.getSubscriptionStatusText())
                        .font(.system(size: 14))
                        .foregroundColor(Color(.secondaryLabel))
                }
                
                Spacer()
                
                // 右侧状态指示器
                VStack(alignment: .trailing, spacing: 8) {
                    if subscriptionManager.isSubscribed {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.title2)
                    } else {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                            .font(.title2)
                    }
                }
            }
            .padding(.horizontal, 4)
            .padding(.vertical, 14)
            .contentShape(Rectangle())
            .onTapGesture {
                showSubscriptionView = true
            }
            
            // 调试按钮（仅在开发环境显示）
            #if DEBUG
            HStack(spacing: 8) {
                Button("同步订阅状态") {
                    Task {
                        await subscriptionManager.syncSubscriptionStatus()
                        subscriptionManager.debugSubscriptionStatus()
                    }
                }
                .buttonStyle(.bordered)
                .font(.caption)
                
                Button("恢复购买") {
                    Task {
                        do {
                            try await subscriptionManager.restorePurchases()
                        } catch {
                            print("恢复购买失败: \(error.localizedDescription)")
                        }
                    }
                }
                .buttonStyle(.bordered)
                .font(.caption)
            }
            .padding(.top, 8)
            #endif
        }
    }
    
    // 主题设置
    private var themeSection: some View {
        Section {
            Picker("theme_mode".localized, selection: $themeManager.themeMode) {
                ForEach(ThemeManager.ThemeMode.allCases) { mode in
                    Text(mode.displayName).tag(mode)
                }
            }
        } header: {
            Text("theme".localized)
        }
    }
    
    // 语言设置
    private var languageSection: some View {
        Section {
            Picker("language_selection".localized, selection: $localizationManager.currentLanguage) {
                ForEach(Language.allCases) { language in
                    HStack {
                        Text(language.flag)
                        Text(language.displayName)
                    }.tag(language)
                }
            }
            .onChange(of: localizationManager.currentLanguage) { _, newLanguage in
                localizationManager.setLanguage(newLanguage)
            }
        } header: {
            Text("language".localized)
        } footer: {
            Text("language_change_notice".localized)
                .font(.footnote)
                .foregroundColor(.secondary)
        }
    }
    
    // 聊天界面设置
    private var chatInterfaceSection: some View {
        Section(header: Text("chat_interface".localized)) {
            Toggle("reverse_alignment".localized, isOn: $reverseAlignment)
            Toggle("default_expand_toolbar".localized, isOn: $defaultExpandTopBar)
            
            Picker("bubble_font_size".localized, selection: $bubbleFontSize) {
                ForEach(FontSize.allCases) { size in
                    Text(size.description).tag(size.rawValue)
                }
            }
            
            // Picker("默认视图模式", selection: $defaultViewMode) {
            //     Text("按天显示").tag("daily")
            //     Text("显示全部").tag("all")
            // }
        }
    }
    
    // 番茄钟设置
    private var pomodoroSection: some View {
        Section(header: Text("pomodoro_settings".localized)) {
            Stepper("work_duration".localized(defaultPomodoroWorkTime), 
                   value: $defaultPomodoroWorkTime, in: 1...60)
            Stepper("break_duration".localized(defaultPomodoroBreakTime), 
                   value: $defaultPomodoroBreakTime, in: 1...30)
        }
    }
    
    // 数据管理
    private var dataManagementSection: some View {
        Section(header: Text("data_management".localized)) {
            Button(action: { 
                if subscriptionManager.isSubscribed {
                    showingExportSheet = true 
                } else {
                    showSubscriptionView = true
                }
            }) {
                HStack {
                    Label("export_data".localized, systemImage: "square.and.arrow.up")
                    Spacer()
                    // if !subscriptionManager.isSubscribed {
                    //     Image(systemName: "book")
                    //         // .foregroundColor(.yellow)
                    // }
                }
                // .foregroundColor(subscriptionManager.isSubscribed ? .primary : .secondary)
            }
        }
    }
    
    // 关于部分
    private var aboutSection: some View {
        Section(header: Text("about".localized)) {
            HStack {
                Text("version".localized)
                Spacer()
                Text(Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "unknown_version".localized)
                    .foregroundColor(.secondary)
            }
            Link("rate_us".localized, destination: URL(string: "https://apps.apple.com/app/id6737453011")!)
            Link("terms_of_service".localized, destination: URL(string: "http://www.unote.site/terms")!)
            Link("privacy_policy".localized, destination: URL(string: "http://www.unote.site/privacy")!)
            Link("technical_support".localized, destination: URL(string: "http://www.unote.site/support")!)
            HStack {
                Text("icp_record".localized)
                    .font(.footnote)
                    .foregroundColor(.secondary)
                Spacer()
            }
        }
    }
}
