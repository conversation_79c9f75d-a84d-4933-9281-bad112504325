import SwiftUI

// 优化的时间戳头部视图
struct TimestampHeaderView: View {
    let timestamp: Date
    let onAppear: () -> Void
    let onDisappear: () -> Void
    
    var body: some View {
        Text(formatTimestamp(timestamp))
            .font(.caption)
            .foregroundColor(.secondary)
            .padding(.horizontal, 8)
            .padding(.vertical, 24)
            .onAppear { onAppear() }
            .onDisappear { onDisappear() }
    }
    
    private func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")
        
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: Date())
        let itemDate = calendar.startOfDay(for: date)
        
        if calendar.isDate(itemDate, inSameDayAs: today) {
            formatter.dateFormat = "HH:mm"
        } else {
            formatter.dateFormat = "month_day_time_format".localized
        }
        
        return formatter.string(from: date)
    }
}

// 优化的聊天项目视图
struct OptimizedChatItemView: View {
    let item: ChatItem
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        ChatItemView(item: item, onEdit: onEdit) {
            onDelete()
        }
        .id(item.id)
        .contextMenu {
            Button(action: onEdit) {
                Label("edit".localized, systemImage: "pencil")
            }
            Button(action: onDelete) {
                Label("delete".localized, systemImage: "trash")
            }
        }
        .clipShape(Rectangle())
        .contentShape(Rectangle())
    }
}