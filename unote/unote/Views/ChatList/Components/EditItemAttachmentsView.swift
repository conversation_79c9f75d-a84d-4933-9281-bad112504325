import SwiftUI
import SwiftData

// MARK: - 编辑项目附件组件
struct EditItemAttachmentsView: View {
    @Bindable var item: ChatItem
    @Binding var isExpanded: <PERSON><PERSON>
    @Binding var showingImagePicker: <PERSON><PERSON>
    @Binding var selectedImages: [UIImage]
    
    var body: some View {
        Section {
            Toggle("attachments".localized, isOn: $isExpanded)
            
            if isExpanded {
                VStack(alignment: .leading, spacing: 12) {
                    // 现有附件
                    if !item.imageData.isEmpty {
                        Text("current_attachments".localized)
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        AttachmentGrid(
                            imageDataArray: item.imageData,
                            onRemove: { imageData in
                                removeAttachment(imageData)
                            }
                        )
                    }
                    
                    // 添加附件按钮
                    Button(action: {
                        showingImagePicker = true
                    }) {
                        HStack {
                            Image(systemName: "plus.circle.fill")
                            Text("add_attachment".localized)
                        }
                        .font(.system(size: 14))
                        .foregroundColor(.blue)
                    }
                }
                .padding(.vertical, 4)
            }
        }
    }
    
    // MARK: - 公共方法
    func loadImages() {
        for image in selectedImages {
            if let imageData = image.jpegData(compressionQuality: 0.8) {
                item.imageData.append(imageData)
            }
        }
        selectedImages.removeAll()
    }
    
    // MARK: - 私有方法
    private func removeAttachment(_ imageData: Data) {
        item.imageData.removeAll { $0 == imageData }
    }
}

// MARK: - 附件网格组件
struct AttachmentGrid: View {
    let imageDataArray: [Data]
    let onRemove: (Data) -> Void
    
    private let columns = [
        GridItem(.adaptive(minimum: 80, maximum: 120), spacing: 8)
    ]
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 8) {
            ForEach(imageDataArray, id: \.self) { imageData in
                AttachmentItem(
                    imageData: imageData,
                    onRemove: {
                        onRemove(imageData)
                    }
                )
            }
        }
    }
}

// MARK: - 单个附件项组件
struct AttachmentItem: View {
    let imageData: Data
    let onRemove: () -> Void
    
    @State private var showFullScreen = false
    
    var body: some View {
        ZStack(alignment: .topTrailing) {
            if let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .scaledToFill()
                    .frame(width: 80, height: 80)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .onTapGesture {
                        showFullScreen = true
                    }
            } else {
                RoundedRectangle(cornerRadius: 8)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 80, height: 80)
                    .overlay {
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    }
            }
            
            // 删除按钮
            Button(action: onRemove) {
                Image(systemName: "xmark.circle.fill")
                    .font(.system(size: 16))
                    .foregroundColor(.red)
                    .background(Color.white, in: Circle())
            }
            .offset(x: 4, y: -4)
        }
        .fullScreenCover(isPresented: $showFullScreen) {
            if let uiImage = UIImage(data: imageData) {
                SimpleImageViewer(image: uiImage)
            }
        }
    }
}

// MARK: - 简单图片查看器
struct SimpleImageViewer: View {
    let image: UIImage
    @Environment(\.dismiss) private var dismiss
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    
    var body: some View {
        ZStack {
            Color.black.ignoresSafeArea()
            
            VStack {
                // 顶部导航栏
                HStack {
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark")
                            .foregroundColor(.white)
                            .font(.system(size: 18, weight: .semibold))
                            .frame(width: 44, height: 44)
                    }
                    
                    Spacer()
                }
                .padding(.horizontal)
                .background(
                    LinearGradient(
                        colors: [.black.opacity(0.6), .clear],
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
                
                Spacer()
                
                // 图片
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .scaleEffect(scale)
                    .offset(offset)
                    .gesture(
                        MagnificationGesture()
                            .onChanged { value in
                                scale = max(1.0, min(value, 4.0))
                            }
                    )
                    .gesture(
                        DragGesture()
                            .onChanged { value in
                                if scale > 1 {
                                    offset = value.translation
                                }
                            }
                            .onEnded { _ in
                                withAnimation(.spring()) {
                                    offset = .zero
                                }
                            }
                    )
                
                Spacer()
            }
        }
        .navigationBarHidden(true)
    }
} 