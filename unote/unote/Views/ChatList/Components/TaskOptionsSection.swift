import SwiftUI

struct TaskOptionsSection: View {
    @Binding var isAllDay: <PERSON><PERSON>
    @Binding var isStartTimeSet: <PERSON><PERSON>
    @Binding var startDate: Date
    @Binding var startTime: Date
    @Binding var isEndTimeSet: Bool
    @Binding var endDate: Date
    @Binding var endTime: Date
    @Binding var repeatOption: RepeatOption
    var pomodoroCountBinding: Binding<Int>
    @Binding var editedItem: ChatItem
    @Binding var isFullScreen: Bool
    
    var body: some View {
        Section {
            Toggle("completed".localized, isOn: $editedItem.completed)
            Toggle("all_day".localized, isOn: $isAllDay)
            
            Toggle("set_start_time".localized, isOn: $isStartTimeSet)
            if isStartTimeSet {
                DateTimePickerRow(title: "start".localized, date: $startDate, time: $startTime, isAllDay: isAllDay)
            }
            
            Toggle("set_end_time".localized, isOn: $isEndTimeSet)
            if isEndTimeSet {
                DateTimePickerRow(title: "end".localized, date: $endDate, time: $endTime, isAllDay: isAllDay)
            }
            
            Menu {
                ForEach(RepeatOption.allCases, id: \.self) { option in
                    Button(action: {
                        repeatOption = option
                    }) {
                        if repeatOption == option {
                            Label(option.description, systemImage: "checkmark")
                        } else {
                            Text(option.description)
                        }
                    }
                }
            } label: {
                HStack {
                    Text("repeat".localized)
                        .font(.body)
                    Spacer()
                    Text(repeatOption.description)
                    Image(systemName: "chevron.up.chevron.down")
                        .font(.caption)
                }
            }
        }
        
        Section(header: Text("pomodoro_section".localized)) {
            HStack {
                Text(String(format: "pomodoro_planned".localized, pomodoroCountBinding.wrappedValue))
                Spacer()
                Stepper("", value: pomodoroCountBinding, in: 0...100)
            }
            Text(String(format: "pomodoro_completed".localized, editedItem.completedPomodoros))
            PomodoroControlView(status: $editedItem.pomodoroStatus, isFullScreen: $isFullScreen)
        }
    }
} 