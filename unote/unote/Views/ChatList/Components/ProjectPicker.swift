import SwiftUI

struct ProjectPicker: View {
    @Binding var selectedProject: Project?
    var projects: [Project]
    
    var body: some View {
        Menu {
            Button(action: {
                selectedProject = nil
            }) {
                Text("none".localized)
            }
            ForEach(projects) { project in
                Button(action: {
                    selectedProject = project
                }) {
                    Text(project.name)
                }
            }
        } label: {
            HStack {
                Text("project".localized)
                    .font(.body)
                Spacer()
                HStack {
                    Text(selectedProject?.name ?? "none".localized)
                    Image(systemName: "chevron.up.chevron.down")
                        .font(.caption)
                }
            }
        }
    }
} 