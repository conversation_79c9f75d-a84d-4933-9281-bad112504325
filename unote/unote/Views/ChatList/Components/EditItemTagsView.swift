import SwiftUI
import SwiftData

// MARK: - 编辑项目标签组件
struct EditItemTagsView: View {
    @Bindable var item: ChatItem
    @Binding var isExpanded: Bool
    @Binding var newTag: String
    @Binding var showingTagInput: Bool
    
    @State private var recentTags: [String] = ["work".localized, "study".localized, "life".localized, "entertainment".localized]
    
    var body: some View {
        Section {
            Toggle("tags".localized, isOn: $isExpanded)
            
            if isExpanded {
                VStack(alignment: .leading, spacing: 12) {
                    // 当前标签列表
                    if !item.tags.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("current_tags".localized)
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            ScrollView(.horizontal, showsIndicators: false) {
                                HStack(spacing: 8) {
                                    ForEach(item.tags, id: \.self) { tag in
                                        TagChip(
                                            text: tag,
                                            isRemovable: true,
                                            onRemove: {
                                                removeTag(tag)
                                            }
                                        )
                                    }
                                }
                            }
                        }
                    }
                    
                    // 添加新标签
                    VStack(alignment: .leading, spacing: 8) {
                        HStack {
                            if showingTagInput {
                                HStack {
                                    TextField("tag_name".localized, text: $newTag)
                                        .textFieldStyle(.roundedBorder)
                                        .font(.system(size: 14))
                                        .onSubmit {
                                            addNewTag()
                                        }
                                    
                                    Button("add_tag_button".localized) {
                                        addNewTag()
                                    }
                                    .disabled(newTag.isEmpty)
                                    
                                    Button("cancel".localized) {
                                        withAnimation {
                                            showingTagInput = false
                                            newTag = ""
                                        }
                                    }
                                }
                            } else {
                                Button(action: {
                                    withAnimation {
                                        showingTagInput = true
                                    }
                                }) {
                                    HStack {
                                        Image(systemName: "plus.circle.fill")
                                        Text("add_tag_placeholder".localized)
                                    }
                                    .font(.system(size: 14))
                                    .foregroundColor(.blue)
                                }
                            }
                        }
                        
                        // 最近使用的标签
                        if !recentTags.isEmpty {
                            VStack(alignment: .leading, spacing: 8) {
                                Text("recent_tags".localized)
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                
                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack(spacing: 8) {
                                        ForEach(recentTags, id: \.self) { tag in
                                            if !item.tags.contains(tag) {
                                                Button(action: {
                                                    withAnimation {
                                                        item.tags.append(tag)
                                                    }
                                                }) {
                                                    Text(tag)
                                                        .font(.system(size: 14))
                                                        .padding(.horizontal, 10)
                                                        .padding(.vertical, 6)
                                                        .background(Color.secondary.opacity(0.1))
                                                        .cornerRadius(16)
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                .listRowSeparator(.hidden)
            }
        }
    }
    
    // MARK: - 私有方法
    private func addNewTag() {
        guard !newTag.isEmpty, !item.tags.contains(newTag) else { return }
        
        withAnimation {
            item.tags.append(newTag)
            newTag = ""
            showingTagInput = false
        }
    }
    
    private func removeTag(_ tag: String) {
        withAnimation {
            item.tags.removeAll { $0 == tag }
        }
    }
}

// MARK: - 标签芯片组件
struct TagChip: View {
    let text: String
    let isRemovable: Bool
    let onRemove: (() -> Void)?
    
    init(text: String, isRemovable: Bool = false, onRemove: (() -> Void)? = nil) {
        self.text = text
        self.isRemovable = isRemovable
        self.onRemove = onRemove
    }
    
    var body: some View {
        HStack(spacing: 4) {
            Text(text)
                .font(.system(size: 14))
                .lineLimit(1)
            
            if isRemovable {
                Button(action: {
                    onRemove?()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 12))
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 10)
        .padding(.vertical, 6)
        .background(Color.blue.opacity(0.1))
        .foregroundColor(.blue)
        .cornerRadius(16)
    }
} 