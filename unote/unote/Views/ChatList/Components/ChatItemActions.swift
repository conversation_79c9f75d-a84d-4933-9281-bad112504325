import SwiftUI
import SwiftData

// MARK: - Chat Item Actions
// 聊天项目的所有操作逻辑，遵循单一职责原则

/// 聊天项目操作管理器
class ChatItemActionManager: ObservableObject {
    
    /// 切换任务完成状态
    static func toggleTaskCompletion(item: ChatItem, isCompleted: inout Bool) {
        isCompleted.toggle()
        item.completed = isCompleted
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }
    
    /// 切换项目类型
    static func toggleItemType(item: ChatItem) {
        switch item.type {
        case .note:
            item.type = .task
        case .task:
            item.type = .note
        case .expense:
            break // 支出类型不切换
        }
    }
    
    /// 切换番茄钟状态
    @MainActor
    static func togglePomodoro(item: ChatItem, pomodoroManager: PomodoroManager) {
        if pomodoroManager.activePomodoro == item {
            pomodoroManager.stopPomodoro()
        } else {
            pomodoroManager.startPomodoro(for: item)
        }
    }
    
    /// 设置任务日期
    static func setTaskDate(item: ChatItem, to option: TaskDateOption) {
        switch option {
        case .today:
            item.plannedDate = Calendar.current.startOfDay(for: Date())
        case .tomorrow:
            item.plannedDate = Calendar.current.date(
                byAdding: .day, 
                value: 1, 
                to: Calendar.current.startOfDay(for: Date())
            )
        }
    }
    
    /// 设置任务时间
    static func setTaskTime(item: ChatItem, hour: Int, minute: Int) {
        let calendar = Calendar.current
        let now = Date()
        let targetDate = item.plannedDate ?? now
        
        if let newTime = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: targetDate) {
            item.plannedTime = newTime
            if item.plannedDate == nil {
                item.plannedDate = calendar.startOfDay(for: now)
            }
        }
    }
    
    /// 取消任务计划
    static func cancelTaskPlan(item: ChatItem) {
        item.plannedDate = nil
        item.plannedTime = nil
    }
    
    /// 复制内容到剪贴板
    static func copyContent(item: ChatItem) {
        UIPasteboard.general.string = item.text
        
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }
    
    /// 格式化时间显示
    static func formatTime(seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}

/// 任务日期选项
enum TaskDateOption {
    case today
    case tomorrow
}

// MARK: - Action Sheets and Menus

/// 聊天项目操作菜单
struct ChatItemActionMenu: View {
    @Bindable var item: ChatItem
    @Binding var isCompleted: Bool
    @Binding var showCustomTimePicker: Bool
    @Binding var showCustomDatePicker: Bool
    @Binding var showCustomPomodoroCount: Bool
    @Binding var tempPomodoroCount: Int
    @EnvironmentObject var pomodoroManager: PomodoroManager
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        Menu {
            // 基础操作
            basicActionsSection
            
            // 任务特定操作
            if item.type == .task {
                taskActionsSection
            }
            
            // 番茄钟操作
            pomodoroActionsSection
            
            // 通用操作
            generalActionsSection
            
        } label: {
            Image(systemName: "ellipsis")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.secondary)
        }
    }
    
    // MARK: - Menu Sections
    
    @ViewBuilder
    private var basicActionsSection: some View {
        // 收藏/取消收藏
        Button(action: {
            item.isFavorite.toggle()
        }) {
            Label(
                item.isFavorite ? "unfavorite".localized : "favorite".localized,
                systemImage: item.isFavorite ? "star.fill" : "star"
            )
        }
        
        // 切换类型
        Button(action: {
            ChatItemActionManager.toggleItemType(item: item)
        }) {
            Label(
                item.type == .task ? "switch_to_note".localized : "switch_to_task".localized,
                systemImage: item.type == .task ? "note.text" : "checklist"
            )
        }
    }
    
    @ViewBuilder
    private var taskActionsSection: some View {
        Section {
            // 标记完成/未完成
            Button(action: {
                ChatItemActionManager.toggleTaskCompletion(item: item, isCompleted: &isCompleted)
            }) {
                Label(
                    isCompleted ? "mark_incomplete".localized : "mark_complete".localized,
                    systemImage: isCompleted ? "circle" : "checkmark.circle"
                )
            }
            
            // 设置计划菜单
            Menu("set_plan".localized) {
                Button("today".localized) {
                    ChatItemActionManager.setTaskDate(item: item, to: .today)
                }
                Button("tomorrow".localized) {
                    ChatItemActionManager.setTaskDate(item: item, to: .tomorrow)
                }
                Button("custom_date".localized) {
                    showCustomDatePicker = true
                }
                Button("custom_time".localized) {
                    showCustomTimePicker = true
                }
                
                if item.plannedDate != nil || item.plannedTime != nil {
                    Divider()
                    Button("cancel_plan".localized, role: .destructive) {
                        ChatItemActionManager.cancelTaskPlan(item: item)
                    }
                }
            }
        }
    }
    
    @ViewBuilder
    private var pomodoroActionsSection: some View {
        Section {
            // 开始/停止番茄钟
            Button(action: {
                Task { @MainActor in
                    ChatItemActionManager.togglePomodoro(item: item, pomodoroManager: pomodoroManager)
                }
            }) {
                Label(
                    pomodoroManager.activePomodoro == item ? "stop_pomodoro".localized : "start_pomodoro".localized,
                    systemImage: "timer"
                )
            }
            .foregroundColor(pomodoroManager.activePomodoro == item ? .red : nil)
            
            // 设置番茄钟数量
            Menu("set_pomodoro_count".localized) {
                ForEach([1, 2, 3, 4], id: \.self) { count in
                    Button(String(format: "count_format".localized, count)) {
                        item.pomodoroCount = count
                    }
                }
                Button("custom_count".localized) {
                    tempPomodoroCount = item.pomodoroCount ?? 1
                    showCustomPomodoroCount = true
                }
            }
        }
    }
    
    @ViewBuilder
    private var generalActionsSection: some View {
        Section {
            // 复制内容
            Button(action: {
                ChatItemActionManager.copyContent(item: item)
            }) {
                Label("copy_content".localized, systemImage: "doc.on.doc")
            }
            
            // 编辑
            Button(action: onEdit) {
                Label("edit".localized, systemImage: "pencil")
            }
            
            // 删除
            Button(role: .destructive, action: onDelete) {
                Label("delete".localized, systemImage: "trash")
            }
        }
    }
}

// MARK: - Custom Pickers

/// 自定义番茄钟数量选择器
struct CustomPomodoroCountPicker: View {
    @Binding var tempPomodoroCount: Int
    @Binding var showCustomPomodoroCount: Bool
    let item: ChatItem
    
    var body: some View {
        VStack(spacing: 20) {
            Text("set_pomodoro_count".localized)
                .font(.headline)
                .padding()
            
            PomodoroStepperView(count: $tempPomodoroCount)
                .padding()
            
            HStack {
                Button("cancel".localized) {
                    showCustomPomodoroCount = false
                }
                
                Button("confirm".localized) {
                    item.pomodoroCount = tempPomodoroCount
                    showCustomPomodoroCount = false
                }
            }
            .padding()
        }
    }
}

/// 时间格式化工具
struct TimeFormatter {
    static func formatDuration(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    static func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter.string(from: date)
    }
}

// MARK: - Environment Keys
// IsAllViewKey 已在 ContentView.swift 中定义

// MARK: - Alignment Extensions

extension HorizontalAlignment {
    var opposite: HorizontalAlignment {
        switch self {
        case .leading:
            return .trailing
        case .trailing:
            return .leading
        default:
            return .center
        }
    }
}
