import SwiftUI
import SwiftData

// MARK: - 编辑项目评论组件
struct EditItemCommentsView: View {
    @Bindable var item: ChatItem
    @Binding var isExpanded: Bool
    @Binding var newComment: String
    
    var body: some View {
        Section {
            Toggle("comment_toggle".localized, isOn: $isExpanded)
            
            if isExpanded {
                VStack(spacing: 6) {
                    ForEach(item.comments) { comment in
                        CommentView(
                            comment: comment,
                            onEdit: { newText in
                                editComment(comment, newText: newText)
                            },
                            onDelete: {
                                deleteComment(comment)
                            }
                        )
                        .listRowSeparator(.hidden)
                    }
                    
                    HStack {
                        TextField("add_comment".localized, text: $newComment)
                            .textFieldStyle(.roundedBorder)
                            .font(.system(size: 14))
                        
                        Button(action: {
                            if !newComment.isEmpty {
                                addComment()
                                newComment = ""
                            }
                        }) {
                            Text("add_tag_button".localized)
                                .font(.system(size: 14))
                        }
                        .disabled(newComment.isEmpty)
                    }
                    .padding(.top, 8)
                }
                .padding(.vertical, 4)
            }
        }
    }
    
    // MARK: - 私有方法
    private func addComment() {
        guard !newComment.isEmpty else { return }
        item.comments.append(Comment(text: newComment))
    }
    
    private func editComment(_ comment: Comment, newText: String) {
        if let index = item.comments.firstIndex(where: { $0.id == comment.id }) {
            item.comments[index].text = newText
        }
    }
    
    private func deleteComment(_ comment: Comment) {
        item.comments.removeAll { $0.id == comment.id }
    }
} 