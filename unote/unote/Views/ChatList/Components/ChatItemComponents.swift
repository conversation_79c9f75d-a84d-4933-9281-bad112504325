import SwiftUI
import SwiftData

// MARK: - Chat Bubble Components
// 重构后的聊天气泡组件，遵循单一职责原则

/// 笔记气泡组件
struct NoteBubbleView: View {
    let item: ChatItem
    let bubbleFontSize: Double
    let colorScheme: ColorScheme
    let isAllView: Bool
    let onImageTap: (Int) -> Void
    let onFullScreen: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ChatBubbleContent(
                item: item,
                backgroundColor: Color.gray.opacity(0.12),
                alignment: .leading,
                bubbleFontSize: bubbleFontSize,
                colorScheme: colorScheme,
                isAllView: isAllView,
                onImageTap: onImageTap,
                onFullScreen: onFullScreen
            )
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .leading)
    }
}

/// 任务气泡组件
struct TaskBubbleView: View {
    let item: ChatItem
    let isCompleted: Bool
    let bubbleFontSize: Double
    let colorScheme: ColorScheme
    let isAllView: Bool
    let onImageTap: (Int) -> Void
    let onFullScreen: () -> Void
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 8) {
            ChatBubbleContent(
                item: item,
                backgroundColor: getBubbleBackgroundColor(),
                alignment: .trailing,
                bubbleFontSize: bubbleFontSize,
                colorScheme: colorScheme,
                isAllView: isAllView,
                onImageTap: onImageTap,
                onFullScreen: onFullScreen
            )
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .trailing)
    }
    
    private func getBubbleBackgroundColor() -> Color {
        let isDarkMode = colorScheme == .dark
        return isCompleted ? 
            Color.green.opacity(isDarkMode ? 0.35 : 0.25) : 
            Color.green.opacity(isDarkMode ? 0.25 : 0.15)
    }
}

/// 收入气泡组件
struct IncomeBubbleView: View {
    let item: ChatItem
    let bubbleFontSize: Double
    let colorScheme: ColorScheme
    let isAllView: Bool
    let onImageTap: (Int) -> Void
    let onFullScreen: () -> Void
    
    var body: some View {
        VStack(alignment: .trailing, spacing: 8) {
            ChatBubbleContent(
                item: item,
                backgroundColor: getBubbleBackgroundColor(),
                alignment: .trailing,
                bubbleFontSize: bubbleFontSize,
                colorScheme: colorScheme,
                isAllView: isAllView,
                onImageTap: onImageTap,
                onFullScreen: onFullScreen
            )
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .trailing)
    }
    
    private func getBubbleBackgroundColor() -> Color {
        let isDarkMode = colorScheme == .dark
        return Color.gray.opacity(isDarkMode ? 0.2 : 0.07)
    }
}

/// 支出气泡组件
struct ExpenseBubbleView: View {
    let item: ChatItem
    let bubbleFontSize: Double
    let colorScheme: ColorScheme
    let isAllView: Bool
    let onImageTap: (Int) -> Void
    let onFullScreen: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            ChatBubbleContent(
                item: item,
                backgroundColor: getBubbleBackgroundColor(),
                alignment: .leading,
                bubbleFontSize: bubbleFontSize,
                colorScheme: colorScheme,
                isAllView: isAllView,
                onImageTap: onImageTap,
                onFullScreen: onFullScreen
            )
        }
        .frame(maxWidth: UIScreen.main.bounds.width * 0.8, alignment: .leading)
    }
    
    private func getBubbleBackgroundColor() -> Color {
        let isDarkMode = colorScheme == .dark
        return Color.gray.opacity(isDarkMode ? 0.2 : 0.07)
    }
}

/// 通用气泡内容组件
struct ChatBubbleContent: View {
    let item: ChatItem
    let backgroundColor: Color
    let alignment: HorizontalAlignment
    let bubbleFontSize: Double
    let colorScheme: ColorScheme
    let isAllView: Bool
    let onImageTap: (Int) -> Void
    let onFullScreen: () -> Void
    
    var body: some View {
        VStack(alignment: alignment, spacing: 2) {
            // 顶部状态指示器
            HStack(spacing: 4) {
                if item.isFavorite {
                    Image(systemName: "star.fill")
                        .foregroundColor(.yellow)
                        .font(.caption2)
                }
                
                if item.type == .task {
                    TaskCompletionIndicator(isCompleted: item.completed)
                }
                
                if !item.text.isEmpty {
                    Text(item.text)
                        .font(.system(size: bubbleFontSize))
                        .foregroundColor(Color(.label))
                        .fixedSize(horizontal: false, vertical: true)
                        .lineLimit(nil)
                        .lineSpacing(2)
                        .tracking(0.2)
                }
                
                // 金额显示
                if let amount = item.amount {
                    AmountDisplayView(amount: amount)
                }
            }
            
            // 分类标签
            if !item.category.isEmpty {
                CategoryTagView(category: item.category)
            }
            
            // 项目名称（仅在全局视图中显示）
            if let projectName = item.project?.name, isAllView {
                ProjectNameView(projectName: projectName)
            }
            
            // 图片内容
            if !item.imageData.isEmpty {
                ImageContentView(
                    imageData: item.imageDataItems ?? [],
                    alignment: alignment,
                    onImageTap: onImageTap,
                    onFullScreen: onFullScreen
                )
            }
        }
        .padding(12)
        .background(
            ZStack(alignment: .leading) {
                RoundedRectangle(cornerRadius: 16)
                    .fill(backgroundColor)
                
                // 番茄钟活跃状态指示器
                if PomodoroManager.shared.activePomodoro == item {
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.green.opacity(0.6), lineWidth: 2)
                }
            }
        )
        .pressEffect()
    }
}

/// 任务完成状态指示器
struct TaskCompletionIndicator: View {
    let isCompleted: Bool
    
    var body: some View {
        Image(systemName: isCompleted ? "checkmark.circle.fill" : "circle")
            .foregroundColor(isCompleted ? .green : .gray)
            .font(.caption)
    }
}

/// 金额显示组件
struct AmountDisplayView: View {
    let amount: Double
    
    var body: some View {
        HStack(spacing: 4) {
            Text("\(amount >= 0 ? "+ " : "- ")\("currency_symbol".localized)\(String(format: "%.2f", abs(amount)))")
                .font(.system(size: 14))
                .fontWeight(.medium)
                .foregroundColor(amount >= 0 ? .red : .green)
        }
    }
}

/// 分类标签组件
struct CategoryTagView: View {
    let category: String
    
    var body: some View {
        Text(category)
            .font(.caption2)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.blue.opacity(0.2))
            .foregroundColor(.blue)
            .cornerRadius(4)
    }
}

/// 项目名称组件
struct ProjectNameView: View {
    let projectName: String
    
    var body: some View {
        Text(projectName)
            .font(.caption2)
            .foregroundColor(.secondary)
            .padding(.horizontal, 6)
            .padding(.vertical, 2)
            .background(Color.secondary.opacity(0.1))
            .cornerRadius(4)
    }
}

/// 图片内容组件
struct ImageContentView: View {
    let imageData: [ImageData]
    let alignment: HorizontalAlignment
    let onImageTap: (Int) -> Void
    let onFullScreen: () -> Void
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 4), count: min(imageData.count, 3)), spacing: 4) {
            ForEach(Array(imageData.enumerated()), id: \.offset) { index, data in
                if let uiImage = UIImage(data: data.data) {
                    ZStack(alignment: .topTrailing) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 80, height: 80)
                            .clipped()
                            .cornerRadius(8)
                            .onTapGesture {
                                onImageTap(index)
                            }
                        
                        if imageData.count > 1 {
                            Text("\(index + 1)/\(imageData.count)")
                                .font(.caption2)
                                .foregroundColor(.secondary)
                                .frame(width: 40)
                            
                            Button(action: onFullScreen) {
                                Image(systemName: "arrow.up.left.and.arrow.down.right")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }
            }
        }
        .padding(.top, 2)
    }
}

// MARK: - Press Effect Modifier
struct PressEffectViewModifier: ViewModifier {
    @State private var isPressed = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.98 : 1)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
    }
}

extension View {
    func pressEffect() -> some View {
        self.modifier(PressEffectViewModifier())
    }
}
