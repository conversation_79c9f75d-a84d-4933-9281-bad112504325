import SwiftUI
import SwiftData

// MARK: - 编辑项目基础表单组件
struct EditItemFormView: View {
    @Bindable var item: ChatItem
    @Binding var tempTitle: String
    @Binding var selectedProject: Project?
    @Binding var useCustomTimestamp: Bool
    @Binding var customTimestamp: Date
    
    @Query private var projects: [Project]
    
    var body: some View {
        // 标题编辑
        Section {
            TextEditor(text: $tempTitle)
                .frame(minHeight: textHeight(tempTitle))
                .fontWeight(.bold)
                .scrollContentBackground(.hidden)
        }
        
        // 类型选择和项目分类
        Section {
            Picker("type_label".localized, selection: $item.type) {
                ForEach([ItemType.note, .task, .expense], id: \.self) { type in
                    Text(type.description).tag(type)
                }
            }
            
            Picker("project".localized, selection: $selectedProject) {
                Text("none".localized).tag(nil as Project?)
                ForEach(projects) { project in
                    Text(project.name).tag(project as Project?)
                }
            }
        }
        
        // 收藏开关
        Section {
            Toggle(isOn: $item.isFavorite) {
                Text("favorite".localized)
            }
        }
        
        // 时间戳设置
        Section {
            // 显示默认创建时间
            HStack {
                Text("creation_time".localized)
                Spacer()
                Text(item.timestamp.formatted(.dateTime))
                    .foregroundColor(.secondary)
            }
            
            // 自定义时间设置
            Toggle("custom_display_time".localized, isOn: $useCustomTimestamp)
            if useCustomTimestamp {
                DatePicker("display_time".localized, selection: $customTimestamp, displayedComponents: [.date, .hourAndMinute])
            }
        }
    }
    
    // MARK: - 辅助方法
    private func textHeight(_ text: String) -> CGFloat {
        let lineHeight: CGFloat = 20
        let minHeight: CGFloat = 40
        let maxHeight: CGFloat = 200
        
        let lines = text.components(separatedBy: .newlines).count
        let estimatedHeight = max(minHeight, CGFloat(lines) * lineHeight)
        
        return min(estimatedHeight, maxHeight)
    }
} 