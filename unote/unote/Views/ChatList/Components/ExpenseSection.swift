import SwiftUI

struct ExpenseSection: View {
    @Binding var amount: String
    @Binding var category: String
    @Binding var editedItem: ChatItem
    
    var body: some View {
        Section(header: Text("accounting_info_header".localized)) {
            TextField("amount_label".localized, text: $amount)
                .keyboardType(.decimalPad)
            TextField("category_label".localized, text: $category)
            Picker("income_expense_type".localized, selection: $editedItem.category) {
                Text("income_tag".localized).tag("income_tag".localized)
                Text("expense_tag".localized).tag("expense_tag".localized)
            }
            .pickerStyle(SegmentedPickerStyle())
        }
    }
} 