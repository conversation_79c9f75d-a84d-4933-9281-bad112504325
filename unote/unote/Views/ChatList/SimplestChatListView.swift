import SwiftUI

// 极简聊天列表视图 - 专注核心功能
struct SimplestChatListView: View {
    @Binding var items: [ChatItem]
    var onDelete: (ChatItem) -> Void
    var onLoadHistory: (() -> Void)?
    @Binding var isViewingAllData: Bool
    let selectedDate: Date
    let isPlannedView: Bool
    let scrollToItem: ChatItem?
    let isLoadingHistory: Bool
    let hasMoreHistory: Bool
    let isNewMessageAdded: Bool
    
    @State private var editingItem: ChatItem?
    @State private var hasScrolledToBottom = false  // 标记是否已滚动到底部
    @State private var lastAnchorId: UUID?  // 记住锚点消息ID
    @EnvironmentObject var pomodoroManager: PomodoroManager
    
    var body: some View {
        ScrollViewReader { proxy in
            ScrollView {
                LazyVStack(spacing: 4) {
                    // 极简加载指示器
                    if isLoadingHistory && hasMoreHistory {
                        ProgressView()
                            .scaleEffect(0.6)
                            .opacity(0.5)
                            .padding(.vertical, 8)
                    }
                    
                    // 消息列表 - 保持原有分组逻辑
                    ForEach(messageGroups, id: \.timestamp) { group in
                        // 时间标题
                        TimestampHeaderView(
                            timestamp: group.timestamp,
                            onAppear: { },
                            onDisappear: { }
                        )
                        
                        // 该组消息
                        ForEach(group.items) { message in
                            OptimizedChatItemView(
                                item: message,
                                onEdit: { editingItem = message },
                                onDelete: { onDelete(message) }
                            )
                            .id(message.id)
                            .onAppear {
                                // 简单预加载：当显示前5条消息时触发
                                checkPreload(message: message)
                            }
                        }
                    }
                    
                    // 底部锚点
                    Color.clear
                        .frame(height: 1)
                        .id("bottom")
                }
            }
            .onAppear {
                // 核心：立即滚动到底部
                scrollToBottomImmediately(proxy: proxy)
            }
            .onChange(of: items.count) { oldCount, newCount in
                handleItemsChange(oldCount: oldCount, newCount: newCount, proxy: proxy)
            }
            .onChange(of: scrollToItem) { _, newItem in
                if let item = newItem {
                    withAnimation {
                        proxy.scrollTo(item.id, anchor: .center)
                    }
                }
            }
        }
        .sheet(item: $editingItem) { item in
            EditChatItemView(
                item: item,
                onSave: { updatedItem in
                    if let index = items.firstIndex(where: { $0.id == updatedItem.id }) {
                        items[index] = updatedItem
                    }
                    editingItem = nil
                },
                onDelete: {
                    onDelete(item)
                    editingItem = nil
                }
            )
        }
    }
    
    // MARK: - 核心方法
    
    /// 立即滚动到底部（无动画）
    private func scrollToBottomImmediately(proxy: ScrollViewProxy) {
        if !items.isEmpty && !hasScrolledToBottom {
            proxy.scrollTo("bottom", anchor: .bottom)
            hasScrolledToBottom = true
            print("🎯 立即定位到底部")
        }
    }
    
    /// 处理消息数量变化 - 优化对话显示逻辑
    private func handleItemsChange(oldCount: Int, newCount: Int, proxy: ScrollViewProxy) {
        if newCount > oldCount {
            if isLoadingHistory {
                // 历史加载：恢复到锚点位置
                print("📖 历史加载完成，恢复锚点位置")
                restoreToAnchor(proxy: proxy)
            } else if isNewMessageAdded {
                // 新消息添加时立即滚动到底部（符合聊天应用逻辑）
                withAnimation(.easeOut(duration: 0.3)) {
                    proxy.scrollTo("bottom", anchor: .bottom)
                }
                print("📩 新消息添加，滚动到底部")
                hasScrolledToBottom = true
            } else {
                print("📊 普通数据变化，不滚动：旧数量=\(oldCount), 新数量=\(newCount)")
            }
        }
    }
    
    /// 检查预加载
    private func checkPreload(message: ChatItem) {
        // 只在前5条消息出现时触发预加载
        guard hasMoreHistory && !isLoadingHistory,
              let messageIndex = items.firstIndex(where: { $0.id == message.id }),
              messageIndex < 5 else {
            return
        }
        
        // 记住当前锚点（选择中间偏上的消息）
        if items.count > 10 {
            let anchorIndex = min(items.count / 3, items.count - 1)
            lastAnchorId = items[anchorIndex].id
        }
        
        print("🔄 触发预加载 - 消息索引: \(messageIndex)")
        onLoadHistory?()
    }
    
    /// 恢复到锚点位置
    private func restoreToAnchor(proxy: ScrollViewProxy) {
        if let anchorId = lastAnchorId {
            // 延迟确保UI更新完成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                withAnimation(.easeInOut(duration: 0.4)) {
                    proxy.scrollTo(anchorId, anchor: .center)
                }
                print("🎯 恢复到锚点位置")
                lastAnchorId = nil
            }
        }
    }
    
    // MARK: - 消息分组（保持原有逻辑）
    private var messageGroups: [MessageGroup] {
        let calendar = Calendar.current
        
        // 按日期分组
        let grouped = Dictionary(grouping: items) { item in
            let date = isPlannedView ? (item.plannedDate ?? item.effectiveTimestamp) : item.effectiveTimestamp
            return calendar.startOfDay(for: date)
        }
        
        // 转换为有序组
        return grouped.map { (date, groupItems) in
            MessageGroup(
                timestamp: date,
                items: groupItems.sorted { 
                    let date1 = isPlannedView ? ($0.plannedDate ?? $0.effectiveTimestamp) : $0.effectiveTimestamp
                    let date2 = isPlannedView ? ($1.plannedDate ?? $1.effectiveTimestamp) : $1.effectiveTimestamp
                    return date1 < date2  // 组内按时间升序
                }
            )
        }
        .sorted { $0.timestamp < $1.timestamp }  // 组间按日期升序
    }
}

// MARK: - 消息组数据结构
struct MessageGroup {
    let timestamp: Date
    let items: [ChatItem]
}