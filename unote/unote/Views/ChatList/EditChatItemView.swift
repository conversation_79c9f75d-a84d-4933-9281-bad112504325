import SwiftUI
import SwiftData

struct EditChatItemView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var pomodoroManager: PomodoroManager
    @Bindable var editedItem: ChatItem
    
    @State private var viewModel: EditChatItemViewModel
    
    let onSave: (ChatItem) -> Void
    let onDelete: () -> Void
    
    init(item: ChatItem, onSave: @escaping (ChatItem) -> Void, onDelete: @escaping () -> Void) {
        self._editedItem = Bindable(item)
        self.onSave = onSave
        self.onDelete = onDelete
        self._viewModel = State(initialValue: EditChatItemViewModel(item: item))
    }
    
    var body: some View {
        NavigationView {
            Form {
                // 基础表单字段
                EditItemFormView(
                    item: editedItem,
                    tempTitle: $viewModel.tempTitle,
                    selectedProject: $viewModel.selectedProject,
                    useCustomTimestamp: $viewModel.useCustomTimestamp,
                    customTimestamp: $viewModel.customTimestamp
                )
                
                // 评论管理
                EditItemCommentsView(
                    item: editedItem,
                    isExpanded: $viewModel.isCommentsExpanded,
                    newComment: $viewModel.newComment
                )
                
                // 标签管理
                EditItemTagsView(
                    item: editedItem,
                    isExpanded: $viewModel.isTagsExpanded,
                    newTag: $viewModel.newTag,
                    showingTagInput: $viewModel.showingTagInput
                )
                
                // 备注部分
                Section {
                    Toggle("notes".localized, isOn: $viewModel.isNotesExpanded)
                    if viewModel.isNotesExpanded {
                        TextEditor(text: $viewModel.notes)
                            .frame(height: 120)
                    }
                }
                
                // 附件管理
                EditItemAttachmentsView(
                    item: editedItem,
                    isExpanded: $viewModel.isAttachmentExpanded,
                    showingImagePicker: $viewModel.showingImagePicker,
                    selectedImages: $viewModel.selectedImages
                )
                
                // 番茄钟模块 (仅在任务类型时显示)
                if editedItem.type == .task {
                    Section {
                        Stepper(value: $viewModel.pomodoroCount, in: 0...10) {
                            HStack {
                                Text("pomodoro_count".localized)
                                Spacer()
                                Text("\(viewModel.pomodoroCount)")
                                    .foregroundColor(.secondary)
                            }
                        }
                        
                        Button("start_pomodoro".localized) {
                            viewModel.isFullScreen = true
                        }
                        .disabled(viewModel.pomodoroCount == 0)
                    }
                }
                
                // 删除按钮
                Section {
                    Button("delete_project_confirm".localized) {
                        viewModel.showingDeleteAlert = true
                    }
                    .foregroundColor(.red)
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("edit_item_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) { 
                        presentationMode.wrappedValue.dismiss()
                    }
                }
                ToolbarItem(placement: .confirmationAction) {
                    Button("done".localized) { 
                        saveAndDismiss()
                    }
                }
            }
            .alert(isPresented: $viewModel.showingDeleteAlert) {
                Alert(
                    title: Text("confirm_delete".localized),
                    message: Text("confirm_delete_item".localized),
                    primaryButton: .destructive(Text("delete".localized)) {
                        onDelete()
                        presentationMode.wrappedValue.dismiss()
                    },
                    secondaryButton: .cancel()
                )
            }
            .sheet(isPresented: $viewModel.showingImagePicker, onDismiss: {
                // 使用 viewModel 的方法加载图片
                viewModel.loadImages(to: editedItem)
            }) {
                ImagePicker(images: $viewModel.selectedImages)
            }
            .fullScreenCover(isPresented: $viewModel.isFullScreen) {
                FullScreenPomodoroView(
                    taskTitle: editedItem.text,
                    onClose: { viewModel.isFullScreen = false },
                    totalPomodoros: viewModel.pomodoroCount
                )
            }
        }
    }
    
    // MARK: - 私有方法
    private func saveAndDismiss() {
        // 保存更改
        editedItem.text = viewModel.tempTitle
        editedItem.project = viewModel.selectedProject
        editedItem.pomodoroCount = viewModel.pomodoroCount
        editedItem.notes = viewModel.notes.isEmpty ? nil : viewModel.notes
        
        if viewModel.useCustomTimestamp {
            editedItem.customTimestamp = viewModel.customTimestamp
        } else {
            editedItem.customTimestamp = nil
        }
        
        onSave(editedItem)
        presentationMode.wrappedValue.dismiss()
    }
}

// MARK: - 编辑聊天项视图模型
@Observable
final class EditChatItemViewModel {
    // UI State
    var showingDeleteAlert = false
    var isFullScreen = false
    var showingImagePicker = false
    var showingTagInput = false
    
    // Form State
    var tempTitle: String
    var selectedProject: Project?
    var pomodoroCount: Int
    var notes: String
    var newComment = ""
    var newTag = ""
    var selectedImages: [UIImage] = []
    
    // Expansion State
    var isNotesExpanded: Bool
    var isCommentsExpanded: Bool
    var isTagsExpanded: Bool
    var isAttachmentExpanded: Bool
    
    // Time State
    var useCustomTimestamp: Bool
    var customTimestamp: Date
    
    init(item: ChatItem) {
        self.tempTitle = item.text
        self.selectedProject = item.project
        self.pomodoroCount = item.pomodoroCount ?? 1
        self.notes = item.notes ?? ""
        
        // 根据内容初始化展开状态
        self.isNotesExpanded = !(item.notes?.isEmpty ?? true)
        self.isCommentsExpanded = !item.comments.isEmpty
        self.isTagsExpanded = !item.tags.isEmpty
        self.isAttachmentExpanded = !item.imageData.isEmpty
        
        self.useCustomTimestamp = item.customTimestamp != nil
        self.customTimestamp = item.customTimestamp ?? item.timestamp
    }
    
    // MARK: - 公共方法
    func loadImages(to item: ChatItem) {
        for image in selectedImages {
            if let imageData = image.jpegData(compressionQuality: 0.8) {
                item.imageData.append(imageData)
            }
        }
        selectedImages.removeAll()
    }
}

