import SwiftUI
import SwiftData

@Observable
final class ChatItemViewModel {
    // State
    var isCompleted: Bool
    var showingOptions = false
    var isDragging = false
    var dragOffset: CGSize = .zero
    var isOverTrash = false
    var isFullScreen = false
    var showImageViewer = false
    var selectedImageIndex = 0
    var showCustomTimePicker = false
    var showCustomDatePicker = false
    var showCustomPomodoroCount = false
    var tempPomodoroCount: Int = 1
    
    // Data
    var item: ChatItem
    var onEdit: () -> Void
    var onDelete: () -> Void
    
    // Dependencies
    @ObservationIgnored var pomodoroManager: PomodoroManager
    
    init(item: ChatItem, onEdit: @escaping () -> Void, onDelete: @escaping () -> Void) {
        self.item = item
        self.isCompleted = item.completed
        self.onEdit = onEdit
        self.onDelete = onDelete
        self.pomodoroManager = MainActor.assumeIsolated {
            PomodoroManager.shared
        }
    }
    
    // MARK: - Public Methods
    
    func toggleTaskCompletion() {
        isCompleted.toggle()
        item.completed = isCompleted
    }
    
    @MainActor
    func togglePomodoro() {
        if pomodoroManager.activePomodoro == item {
            pomodoroManager.stopPomodoro()
        } else {
            pomodoroManager.startPomodoro(for: item)
        }
    }
    
    func toggleItemType() {
        item.type = item.type == .task ? .note : .task
    }
    
    func toggleFavorite() {
        item.isFavorite.toggle()
    }
    
    func copyContent() {
        UIPasteboard.general.string = item.text
    }
    
    func setPomodoroCount(_ count: Int) {
        item.pomodoroCount = count
    }
    
    func setTaskDate(to date: TaskDate) {
        switch date {
        case .today:
            item.plannedDate = Calendar.current.startOfDay(for: Date())
        case .tomorrow:
            item.plannedDate = Calendar.current.date(byAdding: .day, value: 1, to: Calendar.current.startOfDay(for: Date()))
        }
    }
    
    func setTaskTime(hour: Int, minute: Int) {
        let calendar = Calendar.current
        let now = Date()
        let targetDate = item.plannedDate ?? now
        
        if let newTime = calendar.date(bySettingHour: hour, minute: minute, second: 0, of: targetDate) {
            item.plannedTime = newTime
            if item.plannedDate == nil {
                item.plannedDate = calendar.startOfDay(for: now)
            }
        }
    }
    
    func clearPlannedDateTime() {
        item.plannedDate = nil
        item.plannedTime = nil
    }
    
    func formatTimestamp(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "year_month_day_time_format".localized
        return formatter.string(from: date)
    }
    
    func timeString(from seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    func getBubbleBackgroundColor(colorScheme: ColorScheme) -> Color {
        let isDarkMode = colorScheme == .dark
        if let amount = item.amount {
            return amount >= 0 ? Color.gray.opacity(isDarkMode ? 0.2 : 0.07) : Color.gray.opacity(isDarkMode ? 0.2 : 0.07)
        } else if item.type == .task {
            return isCompleted ? Color.green.opacity(isDarkMode ? 0.35 : 0.25) : Color.green.opacity(isDarkMode ? 0.25 : 0.15)
        } else { 
            return Color.gray.opacity(isDarkMode ? 0.2 : 0.1)
        }
    }
    
    // MARK: - Computed Properties
    
    @MainActor
    var isPomodoroActive: Bool {
        pomodoroManager.activePomodoro == item
    }
    
    @MainActor
    var pomodoroProgress: Double {
        isPomodoroActive ? pomodoroManager.progress : 0
    }
    
    @MainActor
    var pomodoroRemainingTime: Int {
        isPomodoroActive ? pomodoroManager.remainingTime : 0
    }
}

enum TaskDate {
    case today
    case tomorrow
} 