import SwiftUI
import SwiftData

struct PomodoroControlView: View {
    @Binding var status: PomodoroStatus
    @Binding var isFullScreen: Bool
    
    var body: some View {
        HStack {
            Button(action: { status = .running }) {
                Image(systemName: "play.fill")
            }
            .disabled(status == .running)
            
            Button(action: { status = .paused }) {
                Image(systemName: "pause.fill")
            }
            .disabled(status != .running)
            
            Button(action: { status = .notStarted }) {
                Image(systemName: "stop.fill")
            }
            .disabled(status == .notStarted)
            
            Spacer()
            
            Button(action: { isFullScreen = true }) {
                Image(systemName: "arrow.up.left.and.arrow.down.right")
            }
            
            Text(status.description)
                .foregroundColor(.secondary)
        }
    }
}



struct ProjectPickerView: View {
    @Binding var selectedProject: Project?
    @Environment(\.presentationMode) var presentationMode
    @Query private var projects: [Project]
    
    var body: some View {
        NavigationView {
            List {
                Button(action: {
                    selectedProject = nil
                    presentationMode.wrappedValue.dismiss()
                }) {
                    HStack {
                        Text("none".localized)
                        Spacer()
                        if selectedProject == nil {
                            Image(systemName: "checkmark")
                        }
                    }
                }
                
                ForEach(projects) { project in
                    Button(action: {
                        selectedProject = project
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        HStack {
                            Text(project.name)
                            Spacer()
                            if selectedProject == project {
                                Image(systemName: "checkmark")
                            }
                        }
                    }
                }
            }
            .navigationTitle("select_project_title".localized)
            .navigationBarItems(trailing: Button("cancel".localized) {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }
}

struct DateTimePickerRow: View {
    let title: String
    @Binding var date: Date
    @Binding var time: Date
    let isAllDay: Bool

    var body: some View {
        HStack {
            Text(title)
            Spacer()
            if isAllDay {
                DatePicker("", selection: $date, displayedComponents: .date)
                    .labelsHidden()
            } else {
                HStack {
                    DatePicker("", selection: $date, displayedComponents: .date)
                        .labelsHidden()
                    DatePicker("", selection: $time, displayedComponents: .hourAndMinute)
                        .labelsHidden()
                }
            }
        }
    }
}

struct DurationPicker: View {
    let title: String
    @Binding var duration: TimeInterval
    
    var body: some View {
        HStack {
            Text(title)
            Spacer()
            Text(formatDuration(duration))
                .foregroundColor(.secondary)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            // 打开持续时间选择器
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: duration) ?? ""
    }
}

struct RepeatOptionRow: View {
    let title: String
    @Binding var repeatOption: RepeatOption
    
    var body: some View {
        HStack {
            Text(title)
            Spacer()
            Text(repeatOption.description)
                .foregroundColor(.secondary)
        }
        .contentShape(Rectangle())
        .onTapGesture {
            // 打开重复选项选择器
        }
    }
}
