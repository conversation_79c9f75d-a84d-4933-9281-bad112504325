import SwiftUI

struct SubscriptionCardView: View {
    let product: SubscriptionProduct
    let isSelected: Bool
    let onSelect: () -> Void
    @Environment(\.isEnabled) private var isEnabled
    @Environment(\.colorScheme) private var colorScheme
    
    // 添加高亮颜色计算属性
    private var highlightColor: Color {
        colorScheme == .dark ? .white : .black
    }
    
    var body: some View {
        Button(action: onSelect) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    // 标题行
                    HStack(spacing: 8) {
                        Text(product.title)
                            .font(.system(size: 17))
                        
                        if let discount = product.discount {
                            Text(discount)
                                .font(.system(size: 12, weight: .medium))
                                .foregroundStyle(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 4)
                                .background(.theme0)
                                .cornerRadius(4)
                        }
                    }
                    
                    // 价格行
                    HStack(alignment: .firstTextBaseline, spacing: 4) {
                        Text(product.price)
                            .font(.system(size: 17, weight: .semibold))
                        
                        if let period = product.period {
                            Text("/" + period)
                                .font(.system(size: 14))
                                .foregroundStyle(.secondary)
                        }
                    }
                    
                    // 功能描述
                    Text(product.features)
                        .font(.caption)
                        .foregroundStyle(.secondary)
                        .padding(.top, 4)
                }
                
                Spacer()
                
                // 选择指示器
                Circle()
                    .strokeBorder(isSelected ? highlightColor : Color(UIColor.systemGray4), lineWidth: 2)
                    .frame(width: 22, height: 22)
                    .overlay {
                        if isSelected {
                            Circle()
                                .fill(highlightColor)
                                .frame(width: 14, height: 14)
                        }
                    }
            }
            .padding(16)
            .background(Color(UIColor.secondarySystemGroupedBackground))
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(isSelected ? highlightColor : Color(UIColor.systemGray4), lineWidth: 2)
            )
            .opacity(isEnabled ? 1 : 0.6)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - 加载视图
struct LoadingView: View {
    var body: some View {
        ZStack {
            Color.black.opacity(0.2)
                .ignoresSafeArea()
            
            ProgressView()
                .progressViewStyle(.circular)
                .tint(.white)
                .padding(24)
                .background(.ultraThinMaterial)
                .cornerRadius(12)
        }
    }
}

// MARK: - 订阅成功动画视图
struct SubscriptionSuccessAnimationView: View {
    @State private var scale = 0.95
    
    var body: some View {
        ZStack {
            // 背景光晕
            Circle()
                .fill(Color.green.opacity(0.1))
                .frame(width: 120, height: 120)
                .scaleEffect(scale)
            
            // 成功图标
            Image(systemName: "checkmark.circle.fill")
                .font(.system(size: 60))
                .foregroundStyle(.green)
                .scaleEffect(scale)
        }
        .onAppear {
            // 使用更温和的弹簧动画
            withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
                scale = 1.0
            }
        }
    }
} 