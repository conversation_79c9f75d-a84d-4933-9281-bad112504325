import SwiftUI

struct SubscriptionFeatureView: View {
    let features: [SubscriptionFeature]
    let isSubscribed: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("pro_features_title".localized)
                .font(.system(size: 20, weight: .semibold))
                .padding(.horizontal, 20)
            
            // 功能列表
            VStack(spacing: 12) {
                ForEach(features) { feature in
                    FeatureRowView(
                        feature: feature,
                        isSubscribed: isSubscribed
                    )
                }
            }
            .padding(.horizontal, 20)
        }
    }
}

// MARK: - 单个功能行视图
struct FeatureRowView: View {
    let feature: SubscriptionFeature
    let isSubscribed: Bool
    
    var body: some View {
        HStack(spacing: 16) {
            // 图标
            Image(systemName: feature.icon)
                .font(.system(size: 24))
                .foregroundColor(isSubscribed ? SubscriptionStyle.successColor : SubscriptionStyle.primaryColor)
                .frame(width: 32, height: 32)
            
            // 文本内容
            VStack(alignment: .leading, spacing: 4) {
                Text(feature.title)
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primary)
                
                Text(feature.description)
                    .font(.system(size: 14))
                    .foregroundColor(.secondary)
                    .fixedSize(horizontal: false, vertical: true)
            }
            
            Spacer()
            
            // 状态指示器
            if isSubscribed {
                Image(systemName: "checkmark.circle.fill")
                    .font(.system(size: 20))
                    .foregroundColor(SubscriptionStyle.successColor)
            } else {
                Image(systemName: "lock.circle")
                    .font(.system(size: 20))
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 8)
    }
} 