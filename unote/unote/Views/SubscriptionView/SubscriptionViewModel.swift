import Foundation
import StoreKit
import SwiftUI

@Observable
final class SubscriptionViewModel {
    // State
    var products: [SubscriptionProduct] = []
    var selectedProduct: SubscriptionProduct?
    var isLoading = false
    var errorMessage: String?
    var showError = false
    var showSuccess = false
    var isPurchasing = false
    var showCancelAlert = false
    
    private let productIdentifiers: Set<String> = Set(StoreProduct.allCases.map { $0.rawValue })
    var dismissAction: () -> Void = {}
    
    init() {
        Task {
            await loadProducts()
        }
    }
    
    // MARK: - Public Methods
    
    @MainActor
    func loadProducts() async {
        isLoading = true
        defer { isLoading = false }
        
        do {
            let products = try await Product.products(for: productIdentifiers)
            self.products = products.map { product in
                let type = StoreProduct(rawValue: product.id)!
                return SubscriptionProduct(
                    id: product.id,
                    type: type,
                    title: getTitleForProduct(type),
                    price: product.displayPrice,
                    description: getDescriptionForProduct(product),
                    isRecommended: type == .yearly,
                    discount: getDiscountForProduct(type),
                    product: product,
                    period: getPeriodForProduct(type),
                    features: getFeaturesForProduct(type)
                )
            }.sorted { first, second in
                if first.type == .yearly { return true }
                if second.type == .yearly { return false }
                return false
            }
            
            if selectedProduct == nil {
                selectedProduct = self.products.first(where: { $0.type == .yearly })
            }
        } catch {
            self.errorMessage = error.localizedDescription
            self.showError = true
        }
    }
    
    @MainActor
    func purchase() async {
        guard let product = selectedProduct?.product else { return }
        isPurchasing = true
        
        do {
            try await SubscriptionManager.shared.purchase(product)
            self.showSuccess = true
            
            // 添加延迟关闭
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                self.dismissAction()
            }
        } catch {
            handlePurchaseError(error)
        }
        
        isPurchasing = false
    }
    
    @MainActor
    func restorePurchases() async {
        isLoading = true
        
        do {
            try await SubscriptionManager.shared.restorePurchases()
            if SubscriptionManager.shared.isSubscribed {
                // 添加短暂延迟以确保状态更新
                try? await Task.sleep(nanoseconds: 500_000_000) // 0.5秒
                self.showSuccess = true
                // 延迟关闭当前视图
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                    self.dismissAction()
                }
            } else {
                self.errorMessage = "subscription_not_found".localized
                self.showError = true
            }
        } catch {
            self.errorMessage = String(format: "purchase_failed".localized, error.localizedDescription)
            self.showError = true
        }
        
        isLoading = false
    }
    
    func getSubscriptionDescription() -> String {
        guard let product = selectedProduct else { return "" }
        return product.description
    }
    
    // MARK: - Private Methods
    
    private func getTitleForProduct(_ type: StoreProduct) -> String {
        switch type {
        case .monthly: return "monthly_subscription".localized
        case .yearly: return "yearly_subscription".localized
        case .lifetime: return "lifetime_subscription".localized
        }
    }
    
    private func getDescriptionForProduct(_ product: Product) -> String {
        switch StoreProduct(rawValue: product.id)! {
        case .monthly:
            return """
            • 每月自动续期
            • 可随时在设置中取消
            • 取消后仍可使用至当前订阅期结束
            """
        case .yearly:
            return """
            • 每年自动续期
            • 可随时在设置中取消
            • 取消后仍可使用至当前订阅期结束
            • 比月付更优惠
            """
        case .lifetime:
            return "lifetime_features_detailed".localized
        }
    }
    
    private func getDiscountForProduct(_ type: StoreProduct) -> String? {
        switch type {
        case .yearly: return "most_popular".localized
        case .lifetime: return "limited_offer".localized
        default: return nil
        }
    }
    
    private func getPeriodForProduct(_ type: StoreProduct) -> String? {
        switch type {
        case .monthly:
            return "monthly".localized
        case .yearly:
            return "yearly".localized
        case .lifetime:
            return nil
        }
    }
    
    private func getFeaturesForProduct(_ type: StoreProduct) -> String {
        switch type {
        case .monthly:
            return "yearly_features_detailed".localized
        case .yearly:
            return "yearly_features_updated".localized
        case .lifetime:
            return "lifetime_features_full".localized
        }
    }
    
    private func handlePurchaseError(_ error: Error) {
        if let subscriptionError = error as? SubscriptionError {
            self.errorMessage = subscriptionError.localizedDescription
        } else if (error as NSError).code == SKError.paymentCancelled.rawValue {
            self.showCancelAlert = true
        } else {
            self.errorMessage = String(format: "purchase_error".localized, error.localizedDescription)
        }
        self.showError = true
    }
} 