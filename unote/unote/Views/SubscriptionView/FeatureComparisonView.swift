import SwiftUI

struct FeatureComparisonView: View {
    var body: some View {
        VStack(spacing: 24) {
            // 免费版特性
            VStack(alignment: .leading, spacing: 16) {
                Text("free_version_includes".localized)
                    .font(.headline)
                
                ForEach(PremiumFeatures.basicFeatures, id: \.self) { feature in
                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                        Text(feature)
                            .font(.subheadline)
                    }
                }
            }
            
            // 高级版特性
            VStack(alignment: .leading, spacing: 16) {
                Text("premium_version_includes".localized)
                    .font(.headline)
                
                ForEach(PremiumFeatures.premiumFeatures, id: \.self) { feature in
                    HStack {
                        Image(systemName: "star.fill")
                            .foregroundColor(.yellow)
                        Text(feature)
                            .font(.subheadline)
                    }
                }
            }
        }
        .padding()
    }
}
