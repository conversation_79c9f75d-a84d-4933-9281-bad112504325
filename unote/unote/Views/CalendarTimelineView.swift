import SwiftUI
import SwiftData

struct CalendarTimelineView: View {
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    
    @Binding var selectedDate: Date
    let chatItems: [ChatItem]
    let filter: ContentFilter
    let project: Project?
    let selectedItemType: ItemType?
    
    let hourHeight: CGFloat = 40
    
    @State private var showSubscription = false
    
    private var filteredChatItems: [ChatItem] {
        if subscriptionManager.isSubscribed {
            return chatItems
        } else {
            // 获取最新的两天的记录
            let sortedDates = chatItems.map { $0.effectiveTimestamp }
                .map { Calendar.current.startOfDay(for: $0) }
                .unique()
                .sorted(by: >)
                .prefix(2)
            
            let latestTwoDays = Set(sortedDates)
            return chatItems.filter { item in
                let itemDay = Calendar.current.startOfDay(for: item.effectiveTimestamp)
                return latestTwoDays.contains(itemDay)
            }
        }
    }
    
    var body: some View {
        ScrollView(.vertical, showsIndicators: false) {
            VStack(alignment: .leading, spacing: 0) {
                ForEach(0..<24) { hour in
                    let items = itemsForHour(hour)
                    HourRow(hour: hour, items: items, hourHeight: hourHeight, itemCount: items.count)
                }
            }
        }
        .overlay(
            Group {
                if !subscriptionManager.isSubscribed && !chatItems.isEmpty {
                    VStack {
                        Spacer()
                        HStack(spacing: 16) {
                            VStack(alignment: .leading, spacing: 6) {
                                Text("upgrade_to_premium".localized)
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                Text("unlock_history".localized)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Image(systemName: "wand.and.sparkles.inverse")
                                .font(.title2)
                                .foregroundStyle(.primary)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(uiColor: .systemBackground))
                                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 2)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.primary.opacity(0.1), lineWidth: 1)
                        )
                        .padding(.horizontal, 16)
                        .padding(.bottom, 16)
                        .onTapGesture {
                            showSubscription = true
                        }
                    }
                }
            }
        )
        .sheet(isPresented: $showSubscription) {
            SubscriptionView()
        }
    }
    
    private func itemsForHour(_ hour: Int) -> [ChatItem] {
        filteredChatItems.filter { item in
            let itemDate = item.startTime ?? item.effectiveTimestamp
            let itemHour = Calendar.current.component(.hour, from: itemDate)
            let itemDay = Calendar.current.startOfDay(for: itemDate)
            let selectedDay = Calendar.current.startOfDay(for: selectedDate)
            
            let matchesDate = itemDay == selectedDay
            
            let matchesFilter: Bool
            switch filter {
            case .all:
                matchesFilter = true
            case .uncategorized:
                matchesFilter = item.project == nil
            case .project:
                matchesFilter = item.project?.id == project?.id
            case .images:
                matchesFilter = !item.imageData.isEmpty
            case .planned:
                matchesFilter = item.startTime != nil
            case .favorites:
                matchesFilter = item.isFavorite == true
            case .pomodoro:
                matchesFilter = item.pomodoroCount != nil
            }
            
            let matchesType = selectedItemType == nil || item.type == selectedItemType
            
            return itemHour == hour && matchesDate && matchesFilter && matchesType
        }
    }
}

struct HourRow: View {
    let hour: Int
    let items: [ChatItem]
    let hourHeight: CGFloat
    let itemCount: Int
    
    var body: some View {
        HStack(spacing: 2) {
            Text(String(format: "%02d:00", hour))
                .font(.caption)
                .frame(width: 48, alignment: .trailing)
                .padding(.trailing, 8)


            if itemCount > 0 {
                Text("\(itemCount)")
                    .font(.caption2)
                    // .padding(4)
                    // .background(Color.gray.opacity(0.2))
                    // .clipShape(Circle())
                    .offset(x: -8)
                    // 定宽高
                    .frame(width: 20, height: 20)
                    .opacity(0.3)
            }
            
            ZStack(alignment: .leading) {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 6) {


                        ForEach(items) { item in
                            ItemView(item: item, hourHeight: hourHeight)
                        }
                    }
                }
                
                
            }
        }
        .frame(height: hourHeight)
        .padding(.vertical, 4)
    }
}

struct ItemView: View {
    let item: ChatItem
    let hourHeight: CGFloat
    @State private var showEditView = false
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.modelContext) private var modelContext

    var body: some View {
        HStack(spacing: 4) {
            HStack {
                // 任务状态指示器
                if item.type == .task {
                    Image(systemName: item.completed ? "checkmark.circle.fill" : "circle")
                        .foregroundColor(item.completed ? .green : .gray)
                        .font(.system(size: 12))
                }
                
                Text(item.text)
                    .font(.caption)
                    .lineLimit(2)
                    .strikethrough(item.type == .task && item.completed)
                    .foregroundColor(item.type == .task && item.completed ? .gray : .primary)
            }
            
            // 图片预览
            if !item.imageData.isEmpty {
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 4) {
                        ForEach(item.imageData.indices, id: \.self) { index in
                            if let uiImage = UIImage(data: item.imageData[index]) {
                                Image(uiImage: uiImage)
                                    .resizable()
                                    .scaledToFill()
                                    .frame(width: 30, height: 30)
                                    .clipShape(RoundedRectangle(cornerRadius: 4))
                            }
                        }
                    }
                }
                .frame(height: 30)
            }
            
            // 时间显示
            if let startTime = item.startTime, let endTime = item.endTime {
                Text("\(formatTime(startTime)) - \(formatTime(endTime))")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            } else {
                Text(formatTime(item.effectiveTimestamp))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .frame(height: hourHeight)
        .background(getBubbleBackgroundColor())
        .cornerRadius(6)
        .onTapGesture {
            showEditView = true
        }
        .sheet(isPresented: $showEditView) {
            EditChatItemView(item: item, onSave: { updatedItem in
                // 处理保存逻辑，如果需要的话
            }, onDelete: {
                modelContext.delete(item)
            })
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: date)
    }
    
    private func getBubbleBackgroundColor() -> Color {
        let isDarkMode = colorScheme == .dark
        if let amount = item.amount {
            return amount >= 0 ? Color.gray.opacity(isDarkMode ? 0.2 : 0.07) : Color.gray.opacity(isDarkMode ? 0.2 : 0.07)
        } else if item.type == .task {
            return item.completed ? Color.green.opacity(isDarkMode ? 0.35 : 0.25) : Color.green.opacity(isDarkMode ? 0.25 : 0.15)
        } else { 
            return Color.gray.opacity(isDarkMode ? 0.2 : 0.1)
        }
    }
}

struct ItemWidthPreferenceKey: PreferenceKey {
    static let defaultValue: CGFloat = 0
    static func reduce(value: inout CGFloat, nextValue: () -> CGFloat) {
        value = max(value, nextValue())
    }
}
