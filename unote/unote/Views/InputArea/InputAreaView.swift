import SwiftUI
import PhotosUI
import Combine
import SwiftData
import AVFoundation

struct InputAreaView: View {
    @Binding var inputText: String
    @Binding var inputAmount: String
    @Binding var selectedType: ItemType
    @Binding var pomodoroCount: Int
    @Binding var tags: [String]
    @Binding var selectedImages: [UIImage]
    @Binding var selectedProject: Project?
    @Binding var selectedFilter: ItemFilter
    
    let currentProject: Project?
    var onSend: () -> Void
    
    @Environment(\.modelContext) private var modelContext
    @Environment(\.colorScheme) private var colorScheme
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var viewModel = InputAreaViewModel()
    @FocusState private var isInputFocused: Bool
    @FocusState private var isAmountInputFocused: Bool
    
    private var isProjectEditable: Bool {
        guard let project = selectedProject ?? currentProject else { return true }
        return project.isEditable(isSubscribed: subscriptionManager.isSubscribed)
    }
    
    var body: some View {
        VStack(spacing: 8) {
            if isProjectEditable {
                editableInputArea
            } else {
                disabledInputArea
            }
        }
        .padding(.horizontal, 16)
        .padding(.top, 12)
        .padding(.bottom, 12)
        .onAppear {
            setupBindings()
            viewModel.onAppear(with: currentProject)
            
            // 调试：检查项目编辑权限
            #if DEBUG
            subscriptionManager.debugProjectEditability(project: currentProject)
            #endif
        }
        .onChange(of: selectedFilter) { oldValue, newValue in
            viewModel.updateSelectedType(for: newValue)
            updateBindings()
        }
        .sheet(isPresented: $viewModel.isImagePickerPresented) {
            ImagePicker(images: $viewModel.selectedImages)
        }
        .sheet(isPresented: $viewModel.isPhotoLibraryPresented) {
            ImagePicker(images: $viewModel.selectedImages, sourceType: .photoLibrary)
        }
        .sheet(isPresented: $viewModel.isCameraPresented) {
            ImagePicker(images: $viewModel.selectedImages, sourceType: .camera)
        }
        .sheet(isPresented: $viewModel.showProjectSheet) {
            ProjectSelectionSheet(viewModel: viewModel)
        }
        .sheet(isPresented: $viewModel.showCategoryPicker) {
            AmountCategoryEditorView(transactionType: viewModel.transactionType, onDismiss: {
                viewModel.loadCategories()
            })
        }
        .sheet(isPresented: $viewModel.showTagManagement) {
            TagManagementView(tagManager: viewModel.tagManager, selectedTags: $viewModel.tags, onTagSelect: { tag in
                viewModel.addTag(tag)
            })
        }
    }
    
    // MARK: - 可编辑输入区域
    private var editableInputArea: some View {
        VStack(spacing: 8) {
            // 工具栏 - 优化动画
            if isInputFocused || isAmountInputFocused {
                InputAreaToolbarView(viewModel: viewModel)
                    .transition(.move(edge: .top).combined(with: .opacity))
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: isInputFocused || isAmountInputFocused)
            }
            
            VStack(spacing: 8) {
                // 金额输入组件
                AmountInputView(viewModel: viewModel)
                
                // 主输入区域
                mainInputArea
            }
            .padding(.vertical, 10)
            .padding(.horizontal, 14)
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(colorScheme == .dark ? 0.2 : 0.1))
                    .shadow(color: .black.opacity(0.05), radius: 2, x: 0, y: 1)
            )
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(
                        isInputFocused || isAmountInputFocused ? 
                        LinearGradient(
                            colors: [Color.blue.opacity(0.3), Color.blue.opacity(0.3)],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ) :
                        LinearGradient(
                            colors: [
                                .white.opacity(0.1),
                                .white.opacity(0.05)
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ),
                        lineWidth: isInputFocused || isAmountInputFocused ? 2 : 1
                    )
                    .animation(.easeInOut(duration: 0.2), value: isInputFocused || isAmountInputFocused)
            )
            
            // 图片预览 - 优化动画
            if !viewModel.selectedImages.isEmpty {
                imagePreviewSection
                    .transition(.asymmetric(
                        insertion: .move(edge: .bottom).combined(with: .opacity),
                        removal: .move(edge: .bottom).combined(with: .opacity)
                    ))
                    .animation(.spring(response: 0.5, dampingFraction: 0.8), value: viewModel.selectedImages.count)
            }
        }
    }
    
    // MARK: - 主输入区域
    private var mainInputArea: some View {
        HStack(spacing: 2) {
            // 类型切换按钮 - 优化动画
            Button(action: {
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
                
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    viewModel.selectedType = viewModel.selectedType == ItemType.note ? ItemType.task : ItemType.note
                }
                isInputFocused = true
                updateBindings()
            }) {
                Image(systemName: viewModel.selectedType == ItemType.note ? "note.text.badge.plus" : "checkmark.circle")
                    .foregroundColor(.blue)
                    .scaleEffect(viewModel.selectedType == ItemType.note ? 1.0 : 1.1)
                    .animation(.spring(response: 0.3), value: viewModel.selectedType)
            }
            .padding(.trailing, 10)
            .padding(.top, 2)

            // 输入区域
            HStack(spacing: 4) {
                // 标签显示 - 优化动画
                ForEach(viewModel.tags, id: \.self) { tag in
                    Text("#\(tag)")
                        .foregroundColor(.blue)
                        .font(.system(size: 14))
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.1))
                        .cornerRadius(4)
                        .transition(.scale.combined(with: .opacity))
                }
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: viewModel.tags)
                
                // 文本输入框 - 优化响应
                TextField(viewModel.placeholderText, text: $viewModel.inputText)
                    .focused($isInputFocused)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onChange(of: viewModel.inputText) { oldValue, newValue in
                        // 优化：减少频繁的状态更新
                        viewModel.handleInputChange(newValue)
                        // 只在必要时更新绑定
                        if oldValue.isEmpty != newValue.isEmpty {
                            updateBindings()
                        }
                    }
                    .onSubmit {
                        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                        impactFeedback.impactOccurred()
                        sendInput()
                    }
            }
            .padding(.vertical, 4)

            // 发送按钮 - 优化动画和反馈
            if viewModel.canSend {
                Button(action: {
                    let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                    impactFeedback.impactOccurred()
                    sendInput()
                }) {
                    Image(systemName: viewModel.formState.isSending ? "hourglass" : "arrow.up.circle.fill")
                        .foregroundColor(viewModel.formState.isSending ? .gray : .blue)
                        .scaleEffect(viewModel.formState.isSending ? 0.9 : 1.0)
                        .rotationEffect(.degrees(viewModel.formState.isSending ? 360 : 0))
                        .animation(.linear(duration: viewModel.formState.isSending ? 1.0 : 0.3).repeatForever(autoreverses: false), value: viewModel.formState.isSending)
                }
                .disabled(viewModel.formState.isSending)
                .transition(.scale.combined(with: .opacity))
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: viewModel.canSend)
    }
    
    // MARK: - 图片预览区域
    private var imagePreviewSection: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 8) {
                ForEach(viewModel.selectedImages.indices, id: \.self) { index in
                    ZStack(alignment: .topTrailing) {
                        Image(uiImage: viewModel.selectedImages[index])
                            .resizable()
                            .scaledToFill()
                            .frame(width: 100, height: 100)
                            .cornerRadius(8)
                            .clipped()
                            .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
                        
                        Button(action: {
                            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                            impactFeedback.impactOccurred()
                            
                            withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                                viewModel.removeImage(at: index)
                                updateBindings()
                            }
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.white)
                                .background(Color.black.opacity(0.6))
                                .clipShape(Circle())
                                .scaleEffect(0.8)
                        }
                        .padding(4)
                    }
                    .transition(.asymmetric(
                        insertion: .scale.combined(with: .opacity),
                        removal: .scale.combined(with: .opacity)
                    ))
                }
            }
            .padding(.horizontal, 8)
        }
        .frame(height: 120)
    }
    
    // MARK: - 不可编辑区域
    private var disabledInputArea: some View {
        HStack(spacing: 2) {
            Image(systemName: "lock.fill")
                .foregroundColor(.secondary)
                .padding(.trailing, 10)
                .padding(.top, 2)
            
            VStack(alignment: .leading, spacing: 4) {
                Text("view_only_mode".localized)
                    .foregroundColor(.gray)
                Text("upgrade_to_unlock_editing".localized)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.vertical, 10)
        .padding(.horizontal, 14)
        .background(Color.gray.opacity(colorScheme == .dark ? 0.2 : 0.1))
        .cornerRadius(12)
        .onTapGesture {
            let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene
            if let rootViewController = windowScene?.windows.first?.rootViewController {
                let subscriptionView = UIHostingController(rootView: SubscriptionView())
                rootViewController.present(subscriptionView, animated: true)
            }
        }
    }
    
    // MARK: - Private Methods
    
    private func setupBindings() {
        viewModel.inputText = inputText
        viewModel.inputAmount = inputAmount
        viewModel.selectedType = selectedType
        viewModel.pomodoroCount = pomodoroCount
        viewModel.tags = tags
        viewModel.selectedImages = selectedImages
        viewModel.selectedProject = selectedProject
    }
    
    private func updateBindings() {
        inputText = viewModel.inputText
        inputAmount = viewModel.inputAmount
        selectedType = viewModel.selectedType
        pomodoroCount = viewModel.pomodoroCount
        tags = viewModel.tags
        selectedImages = viewModel.selectedImages
        selectedProject = viewModel.selectedProject
    }
    
    private func sendInput() {
        guard isProjectEditable else {
            return
        }
        
        // 优化：立即给予视觉反馈
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
        
        Task {
            await viewModel.sendInput(with: modelContext, currentProject: currentProject) {
                onSend()
                updateBindings()
            }
        }
    }
}





