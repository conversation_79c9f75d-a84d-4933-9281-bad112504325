import SwiftUI
import Foundation
import SwiftData

// 新增:类型选择视图
struct CategoryPickerView: View {
    @Binding var selectedCategory: String
    let transactionType: TransactionType
    @Environment(\.dismiss) private var dismiss
    @State private var categories: [String] = []
    @State private var newCategory: String = ""
    @State private var showAddCategoryAlert: Bool = false

    var body: some View {
        NavigationView {
            List {
                ForEach(categories, id: \.self) { category in
                    Button(action: {
                        selectedCategory = category
                        dismiss()
                    }) {
                        Text(category)
                    }
                }
                .onDelete(perform: deleteCategory)
            }
            .navigationTitle(transactionType == .income ? "income_type_title".localized : "expense_type_title".localized)
            .navigationBarItems(
                leading: EditButton(),
                trailing: <PERSON><PERSON>("add".localized) {
                    showAddCategoryAlert = true
                }
            )
        }
        .onAppear {
            loadCategories()
        }
        .alert("add_new_type".localized, isPresented: $showAddCategoryAlert) {
            TextField("new_type_name".localized, text: $newCategory)
                            Button("cancel".localized, role: .cancel) { }
            Button("add".localized) {
                addNewCategory()
            }
        } message: {
            Text("enter_new_type_name".localized)
        }
    }

    private func loadCategories() {
        let key = transactionType == .income ? "incomeCategories" : "expenseCategories"
        categories = UserDefaults.standard.stringArray(forKey: key) ?? []
        if categories.isEmpty {
            // 如果没有保存的类型,添加一些默认类型
            categories = transactionType == .income ? 
            ["salary_category".localized, "investment_category".localized, "other_category".localized] : 
            ["food_category".localized, "transport_category".localized, "shopping_category".localized, "other_category".localized]
            saveCategories()
        }
    }

    private func saveCategories() {
        let key = transactionType == .income ? "incomeCategories" : "expenseCategories"
        UserDefaults.standard.set(categories, forKey: key)
    }

    private func addNewCategory() {
        guard !newCategory.isEmpty else { return }
        categories.append(newCategory)
        saveCategories()
        newCategory = ""
    }

    private func deleteCategory(at offsets: IndexSet) {
        categories.remove(atOffsets: offsets)
        saveCategories()
    }
}

// 新增类型编辑视图
struct CategoryEditorView: View {
    let transactionType: TransactionType
    @State private var categories: [String] = []
    @State private var newCategory: String = ""
    @Environment(\.dismiss) private var dismiss
    let onDismiss: () -> Void
    @State private var editingCategory: String = ""
    @State private var isEditing: Bool = false
    @State private var editingIndex: Int = -1
    @Environment(\.modelContext) private var modelContext

    var body: some View {
        NavigationView {
            List {
                Section(header: Text("add_new_type".localized)) {
                    HStack {
                        TextField("new_type_name".localized, text: $newCategory)
                        Button("add".localized) {
                            addNewCategory()
                        }
                        .disabled(newCategory.isEmpty)
                    }
                }

                Section(header: Text("existing_types_section".localized)) {
                    ForEach(Array(categories.enumerated()), id: \.element) { index, category in
                        if editingIndex == index {
                            HStack {
                                TextField("type_name_placeholder".localized, text: $editingCategory)
                                    .submitLabel(.done)
                                    .onSubmit {
                                        updateCategory(at: index)
                                    }
                                Button("cancel".localized) {
                                    cancelEditing()
                                }
                            }
                        } else {
                            Text(category)
                                .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                                    Button(role: .destructive) {
                                        categories.remove(at: index)
                                        saveCategories()
                                    } label: {
                                        Label("delete".localized, systemImage: "trash")
                                    }
                                    
                                    Button {
                                        startEditing(category: category, at: index)
                                    } label: {
                                        Label("edit".localized, systemImage: "pencil")
                                    }
                                    .tint(.orange)
                                }
                        }
                    }
                    .onDelete(perform: deleteCategory)
                }
            }
                            .navigationTitle(transactionType == .income ? "edit_income_type_title".localized : "edit_expense_type_title".localized)
            .navigationBarItems(
                                    leading: Button("done".localized) { 
                    dismiss()
                    onDismiss()
                },
                trailing: EditButton()
            )
        }
        .onAppear(perform: loadCategories)
                        .alert("error".localized, isPresented: .constant(false)) {
                    Button("confirm".localized, role: .cancel) { }
                } message: {
                    Text("type_name_empty_error".localized)
                }
    }

    private func loadCategories() {
        let key = transactionType == .income ? "incomeCategories" : "expenseCategories"
        categories = UserDefaults.standard.stringArray(forKey: key) ?? []
        
    }

    private func saveCategories() {
        let key = transactionType == .income ? "incomeCategories" : "expenseCategories"
        UserDefaults.standard.set(categories, forKey: key)
    }

    private func addNewCategory() {
        guard !newCategory.isEmpty else { return }
        categories.append(newCategory)
        saveCategories()
        newCategory = ""
    }

    private func deleteCategory(at offsets: IndexSet) {
        categories.remove(atOffsets: offsets)
        saveCategories()
    }

    private func startEditing(category: String, at index: Int) {
        editingCategory = category
        editingIndex = index
        isEditing = true
    }

    private func cancelEditing() {
        editingCategory = ""
        editingIndex = -1
        isEditing = false
    }

    private func updateCategory(at index: Int) {
        guard !editingCategory.isEmpty else { return }
        let oldCategory = categories[index]
        categories[index] = editingCategory
        saveCategories()
        
        // 更新所有使用该类别的 ChatItem
        do {
            let descriptor = FetchDescriptor<ChatItem>(
                predicate: #Predicate<ChatItem> { item in
                    item.category == oldCategory
                }
            )
            let itemsToUpdate = try modelContext.fetch(descriptor)
            
            for item in itemsToUpdate {
                item.category = editingCategory
            }
            
            try modelContext.save()
        } catch {
                            print(String(format: "update_category_failed".localized, error.localizedDescription))
        }
        
        cancelEditing()
    }
}