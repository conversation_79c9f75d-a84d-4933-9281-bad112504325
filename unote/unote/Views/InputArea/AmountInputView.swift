import SwiftUI

struct AmountInputView: View {
    @Bindable var viewModel: InputAreaViewModel
    @FocusState private var isAmountInputFocused: Bool
    
    var body: some View {
        if viewModel.showAmountInput {
            VStack(spacing: 8) {
                HStack {
                    // 收支类型选择器
                    Picker("", selection: $viewModel.transactionType) {
                        Text("expense".localized).tag(TransactionType.expense)
                        Text("income".localized).tag(TransactionType.income)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .frame(width: 100)
                    .onChange(of: viewModel.transactionType) { oldValue, newValue in
                        viewModel.loadCategories()
                        viewModel.selectedCategory = ""
                    }

                    // 金额输入
                    HStack(spacing: 0) {
                        Text(viewModel.transactionType == .income ? "+" : "-")
                            .foregroundColor(viewModel.transactionType == .income ? .red : .green)
                        Text("¥")
                            .foregroundColor(viewModel.transactionType == .income ? .red : .green)
                        TextField("amount_zero".localized, text: $viewModel.inputAmount)
                            .focused($isAmountInputFocused)
                            .keyboardType(.decimalPad)
                            .textFieldStyle(PlainTextFieldStyle())
                            .foregroundColor(viewModel.transactionType == .income ? .red : .green)
                            .frame(width: max(40, CGFloat(viewModel.inputAmount.count * 11)))
                    }
                    .fontWeight(.bold)
                    
                    Spacer()
                    
                    // 类别选择器
                    categoryPickerMenu
                }
                
                Divider()
                    .padding(.vertical, 2)
            }
        }
    }
    
    private var categoryPickerMenu: some View {
        Menu {
            ForEach(viewModel.categories, id: \.self) { category in
                Button(action: {
                    viewModel.selectedCategory = category
                }) {
                    HStack {
                        Text(category)
                        if viewModel.selectedCategory == category {
                            Image(systemName: "checkmark")
                                .foregroundColor(.black)
                        }
                    }
                }
            }
            
            Divider()
            
            Button(action: {
                viewModel.showCategoryPicker = true
            }) {
                Label("edit_type_label".localized, systemImage: "pencil")
            }
        } label: {
            HStack {
                Text(viewModel.selectedCategory.isEmpty ? "type_label".localized : viewModel.selectedCategory)
                    .font(.caption)
                    .foregroundColor(viewModel.selectedCategory.isEmpty ? .gray : .primary)
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .padding(.vertical, 6)
            .padding(.horizontal, 10)
            .background(Color.gray.opacity(0.1))
            .cornerRadius(8)
        }
    }
}

// MARK: - 类别编辑器视图
struct AmountCategoryEditorView: View {
    let transactionType: TransactionType
    let onDismiss: () -> Void
    @Environment(\.dismiss) private var dismiss
    @State private var categories: [String] = []
    @State private var newCategory = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 16) {
                // 添加新类别
                HStack {
                    TextField("new_category_placeholder".localized, text: $newCategory)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                    
                    Button("add_button".localized) {
                        addCategory()
                    }
                    .disabled(newCategory.isEmpty)
                }
                .padding(.horizontal)
                
                // 现有类别列表
                List {
                    ForEach(categories, id: \.self) { category in
                        Text(category)
                    }
                    .onDelete(perform: deleteCategories)
                }
            }
            .navigationTitle(transactionType == .income ? "income_categories".localized : "expense_categories".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("done".localized) {
                        saveCategories()
                        onDismiss()
                        dismiss()
                    }
                }
            }
        }
        .onAppear {
            loadCategories()
        }
    }
    
    private func loadCategories() {
        let key = transactionType == .income ? "incomeCategories" : "expenseCategories"
        categories = UserDefaults.standard.stringArray(forKey: key) ?? []
        if categories.isEmpty {
            categories = transactionType == .income ?
            ["salary_category".localized, "investment_category".localized, "other_category".localized] :
            ["food_category".localized, "transport_category".localized, "shopping_category".localized, "other_category".localized]
        }
    }
    
    private func addCategory() {
        let trimmed = newCategory.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmed.isEmpty && !categories.contains(trimmed) else { return }
        
        categories.append(trimmed)
        newCategory = ""
    }
    
    private func deleteCategories(offsets: IndexSet) {
        categories.remove(atOffsets: offsets)
    }
    
    private func saveCategories() {
        let key = transactionType == .income ? "incomeCategories" : "expenseCategories"
        UserDefaults.standard.set(categories, forKey: key)
    }
} 