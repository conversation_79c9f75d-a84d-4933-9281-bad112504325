import SwiftUI
import SwiftData

struct ProjectSelectionSheet: View {
    @Bindable var viewModel: InputAreaViewModel
    @Query private var projects: [Project]
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List(filteredProjects) { project in
                Button(action: {
                    viewModel.selectProject(project)
                    dismiss()
                }) {
                    HStack {
                        Text(project.name)
                            .foregroundColor(.primary)
                        Spacer()
                        if project == viewModel.selectedProject {
                            Image(systemName: "checkmark")
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
            .searchable(text: $viewModel.projectSearchText, prompt: "search_projects_placeholder".localized)
            .navigationTitle("select_project_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("cancel".localized) {
                        dismiss()
                    }
                }
            }
        }
    }
    
    private var filteredProjects: [Project] {
        if viewModel.projectSearchText.isEmpty {
            return projects
        }
        return projects.filter { 
            $0.name.lowercased().contains(viewModel.projectSearchText.lowercased()) 
        }
    }
} 