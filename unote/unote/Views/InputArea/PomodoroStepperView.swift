import SwiftUI

struct PomodoroStepperView: View {
    @Binding var count: Int
    
    var body: some View {
        HStack(spacing: 8) {
            But<PERSON>(action: { if count > 0 { count -= 1 } }) {
                Image(systemName: "minus.circle.fill")
                    .foregroundColor(.gray)
            }
            
            Text("\(count) 🍅")
                .frame(minWidth: 40)
                .font(.system(size: 16))
            
            Button(action: { if count < 10 { count += 1 } }) {
                Image(systemName: "plus.circle.fill")
                    .foregroundColor(.gray)
            }
        }
        .padding(.vertical, 8)
    }
}