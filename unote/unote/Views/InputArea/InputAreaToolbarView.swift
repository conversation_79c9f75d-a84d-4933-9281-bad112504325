import SwiftUI
import SwiftData

struct InputAreaToolbarView: View {
    @Bindable var viewModel: InputAreaViewModel
    @Query private var projects: [Project]
    
    var body: some View {
        HStack(spacing: 12) {
            // 图片库按钮
            Button(action: { 
                viewModel.isPhotoLibraryPresented = true 
            }) {
                Image(systemName: "photo")
                    .foregroundColor(.gray)
            }
            
            // 相机按钮
            Button(action: {
                viewModel.checkAndRequestCameraPermission()
            }) {
                Image(systemName: "camera")
                    .foregroundColor(.gray)
            }

            // 记账按钮
            Button(action: {
                withAnimation {
                    viewModel.showAmountInput.toggle()
                }
            }) {
                Image(systemName: "creditcard")
                    .foregroundColor(viewModel.showAmountInput ? .blue : .gray)
            }

            // 项目选择器
            projectPickerMenu

            // 番茄钟选择器
            if viewModel.selectedType == .task {
                pomodoroCountMenu
            }

            Spacer()
            
            // 标签按钮
            tagMenu

            // 键盘收起按钮
            But<PERSON>(action: {
                UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
            }) {
                Image(systemName: "keyboard.chevron.compact.down")
                    .foregroundColor(.gray)
            }
        }
        .padding(.horizontal, 2)
        .padding(.vertical, 2)
        .font(.system(size: 18))
    }
    
    // MARK: - 项目选择器
    private var projectPickerMenu: some View {
        Menu {
            ForEach(projects) { project in
                Button(action: {
                    viewModel.selectProject(project)
                }) {
                    HStack {
                        Text(project.name)
                        Spacer()
                        if project == viewModel.selectedProject {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
        } label: {
            HStack(spacing: 2) {
                Text(viewModel.selectedProject?.name ?? "category".localized)
                    .font(.caption)
                    .foregroundColor(viewModel.selectedCategory.isEmpty ? .gray : .primary)
                    .lineLimit(1)
                    .truncationMode(.tail)
                Image(systemName: "chevron.down")
                    .font(.caption)
                    .foregroundColor(.gray)
            }
            .foregroundColor(.primary)
            .padding(.horizontal, 8)
            .padding(.vertical, 5)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
        }
    }
    
    // MARK: - 番茄钟选择器
    private var pomodoroCountMenu: some View {
        Menu {
            ForEach(1...8, id: \.self) { number in
                Button(action: {
                    viewModel.pomodoroCount = number
                }) {
                    HStack {
                        Text("\(number)🍅")
                        Spacer()
                        if viewModel.pomodoroCount == number {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
            
            Divider()
            
            Button(action: {
                viewModel.isInputFocused = true
            }) {
                Label("custom_label".localized, systemImage: "slider.horizontal.3")
            }
        } label: {
            HStack(spacing: 2) {
                Text("🍅+\(viewModel.pomodoroCount)")
                    .font(.caption)
            }
            .foregroundColor(.text0)
            .fontWeight(.medium)
            .padding(.horizontal, 8)
            .padding(.vertical, 5)
            .background(Color.gray.opacity(0.2))
            .cornerRadius(8)
        }
    }
    
    // MARK: - 标签菜单
    private var tagMenu: some View {
        Menu {
            ForEach(viewModel.tagManager.frequentTags.prefix(5)) { tagInfo in
                Button(action: {
                    viewModel.toggleTag(tagInfo.tag)
                }) {
                    HStack {
                        Text(tagInfo.tag)
                        Spacer()
                        if viewModel.tags.contains(tagInfo.tag) {
                            Image(systemName: "checkmark")
                        }
                    }
                }
            }
            
            if !viewModel.tagManager.frequentTags.isEmpty {
                Divider()
            }
            
            Button(action: {
                viewModel.showTagManagement = true
            }) {
                Label("manage_tags_label".localized, systemImage: "tag")
            }
        } label: {
            Image(systemName: "tag")
                .foregroundColor(viewModel.tags.isEmpty ? .gray : .blue)
        }
    }
} 