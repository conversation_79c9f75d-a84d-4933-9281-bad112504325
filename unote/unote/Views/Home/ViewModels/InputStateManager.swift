import SwiftUI
import Combine

// MARK: - 输入状态管理器
@MainActor
final class InputStateManager: ObservableObject {
    // 输入相关状态，独立管理避免影响主页面渲染
    @Published var inputText = ""
    @Published var inputAmount = ""
    @Published var selectedType: ItemType = .note
    @Published var pomodoroCount = 0
    @Published var tags: [String] = []
    @Published var selectedImages: [UIImage] = []
    @Published var selectedFilter: ItemFilter = .all
    
    // 使用弱引用避免循环依赖
    weak var uiStateManager: UIStateManager?
    
    func resetInputState() {
        inputText = ""
        inputAmount = ""
        selectedType = .note
        pomodoroCount = 0
        tags = []
        selectedImages = []
    }
    
    func handleSend() {
        guard let uiStateManager = uiStateManager else { return }
        
        if let project = uiStateManager.selectedProject {
            uiStateManager.navigateToContent(
                project: project,
                filter: .project,
                scrollToItem: nil
            )
        } else {
            uiStateManager.navigateToContent(
                project: nil,
                filter: .all,
                scrollToItem: nil
            )
        }
        resetInputState()
    }
    
    func updateSelectedType(for filter: ItemFilter) {
        switch filter {
        case .tasks:
            selectedType = .task
        case .notes:
            selectedType = .note
        case .all:
            break // 保持当前选择
        }
    }
}