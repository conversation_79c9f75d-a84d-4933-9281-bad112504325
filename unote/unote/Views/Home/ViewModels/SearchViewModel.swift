import SwiftUI
import SwiftData
import Combine

// MARK: - 独立的搜索视图模型
@MainActor
final class SearchViewModel: ObservableObject {
    @Published var searchResults: [(Project?, ChatItem)] = []
    @Published private(set) var isSearching = false
    @Published var searchText = "" {
        didSet {
            // 防抖处理：300ms延迟
            searchWorkItem?.cancel()
            searchWorkItem = DispatchWorkItem {
                Task {
                    await self.triggerSearch()
                }
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: searchWorkItem!)
        }
    }
    
    private var searchWorkItem: DispatchWorkItem?
    private var allChatItems: [ChatItem] = []
    private let maxSearchResults = 10
    
    func updateChatItems(_ items: [ChatItem]) {
        // 只在数据真正变化时更新，避免不必要的重新渲染
        if allChatItems.count != items.count {
            allChatItems = items
        }
    }
    
    private func triggerSearch() async {
        guard !searchText.isEmpty else {
            searchResults = []
            isSearching = false
            return
        }
        
        isSearching = true
        
        // 在后台执行搜索，避免阻塞UI
        let results = await performSearchInBackground()
        
        searchResults = results
        isSearching = false
    }
    
    private func performSearchInBackground() async -> [(Project?, ChatItem)] {
        let searchTerms = searchText.lowercased()
            .components(separatedBy: .whitespacesAndNewlines)
            .filter { !$0.isEmpty }
        
        return await Task.detached(priority: .userInitiated) { [allChatItems, maxSearchResults] in
            let matches = allChatItems.compactMap { item -> (Project?, ChatItem, Int)? in
                let score = Self.calculateSearchScore(item: item, terms: searchTerms)
                guard score > 0 else { return nil }
                return (item.project, item, score)
            }
            .sorted { $0.2 > $1.2 }
            .prefix(maxSearchResults)
            .map { ($0.0, $0.1) }
            
            return Array(matches)
        }.value
    }
    
    private static nonisolated func calculateSearchScore(item: ChatItem, terms: [String]) -> Int {
        var score = 0
        let itemText = item.text.lowercased()
        
        for term in terms {
            if itemText.contains(term) {
                score += 3
                if itemText.components(separatedBy: .newlines)
                    .first?.contains(term) == true {
                    score += 2
                }
            }
            
            if item.tags.contains(where: { $0.lowercased().contains(term) }) {
                score += 2
            }
        }
        
        if Calendar.current.isDateInToday(item.timestamp) {
            score += 1
        }
        return score
    }
}