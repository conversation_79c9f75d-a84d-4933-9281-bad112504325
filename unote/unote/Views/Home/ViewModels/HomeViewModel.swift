import SwiftUI
import SwiftData
import Combine

// MARK: - 优化后的主页视图模型（减少@Published属性）
@MainActor
final class HomeViewModel: ObservableObject {
    
    // 分离的状态管理器
    @Published var uiStateManager = UIStateManager()
    @Published var searchViewModel = SearchViewModel()
    @Published var inputStateManager = InputStateManager()
    
    // 搜索结果的选中状态
    @Published var selectedSearchResult: ChatItem?
    
    private var cancellables = Set<AnyCancellable>()
    
    init() {
        setupStateManagers()
        setupNotificationObservers()
    }
    
    private func setupStateManagers() {
        // 建立状态管理器之间的关联
        inputStateManager.uiStateManager = uiStateManager
    }
    
    // MARK: - Public Methods
    func handleProjectEdit(_ project: Project) {
        uiStateManager.handleProjectEdit(project)
    }
    
    func handleSend() {
        inputStateManager.handleSend()
    }
    
    func resetInputState() {
        inputStateManager.resetInputState()
    }
    
    func deleteProject(_ project: Project, modelContext: ModelContext) {
        uiStateManager.deleteProject(project, modelContext: modelContext)
    }
    
    func updateChatItems(_ items: [ChatItem]) {
        searchViewModel.updateChatItems(items)
    }
    
    // MARK: - Search Methods（已移到SearchViewModel）
    func triggerSearch(query: String, allChatItems: [ChatItem]) {
        // 更新搜索数据
        searchViewModel.updateChatItems(allChatItems)
        // 搜索文本的更新会自动触发搜索（防抖处理）
        searchViewModel.searchText = query
    }
    
    func navigateToSearchResult(project: Project?, item: ChatItem) {
        selectedSearchResult = item
        uiStateManager.navigateToContent(
            project: project,
            filter: project != nil ? .project : .all,
            scrollToItem: item
        )
    }
    
    // MARK: - Private Methods
    private func setupNotificationObservers() {
        NotificationCenter.default.publisher(for: NSNotification.Name("OpenProject"))
            .sink { [weak self] notification in
                if let project = notification.userInfo?["project"] as? Project {
                    self?.uiStateManager.navigateToContent(
                        project: project,
                        filter: .project,
                        scrollToItem: nil
                    )
                }
            }
            .store(in: &cancellables)
        
        NotificationCenter.default.publisher(for: NSNotification.Name("OpenUncategorized"))
            .sink { [weak self] _ in
                self?.uiStateManager.navigateToContent(
                    project: nil,
                    filter: .uncategorized,
                    scrollToItem: nil
                )
            }
            .store(in: &cancellables)
    }
}

 