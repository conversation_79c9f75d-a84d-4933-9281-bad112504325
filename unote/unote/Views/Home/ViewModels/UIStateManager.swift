import SwiftUI
import SwiftData
import Combine

// MARK: - 轻量级UI状态管理器
@MainActor
final class UIStateManager: ObservableObject {
    // 只保留必要的UI状态，减少@Published属性数量
    @Published var showingAddProject = false
    @Published var showingSettings = false 
    @Published var showingStats = false
    @Published var editingProject: Project?
    
    // 导航状态
    @Published var navigationPath = NavigationPath()
    @Published var selectedContentFilter: ContentFilter = .all
    
    // 使用非@Published的私有状态，避免不必要的UI更新
    private var _selectedProject: Project?
    private var _selectedItemType: ItemType?
    
    var selectedProject: Project? {
        get { _selectedProject }
        set { 
            if _selectedProject?.id != newValue?.id {
                _selectedProject = newValue
            }
        }
    }
    
    var selectedItemType: ItemType? {
        get { _selectedItemType }
        set {
            if _selectedItemType != newValue {
                _selectedItemType = newValue
            }
        }
    }
    
    func handleProjectEdit(_ project: Project) {
        editingProject = project
    }
    
    func navigateToContent(project: Project?, filter: ContentFilter, scrollToItem: ChatItem? = nil) {
        navigationPath.append(NavigationDestination.content(
            project: project,
            filter: filter,
            scrollToItem: scrollToItem
        ))
    }
    
    func deleteProject(_ project: Project, modelContext: ModelContext) {
        modelContext.delete(project)
        do {
            try modelContext.save()
        } catch {
            print(String(format: "delete_project_error".localized, error.localizedDescription))
        }
    }
}

// MARK: - 导航目标枚举
extension UIStateManager {
    enum NavigationDestination: Hashable {
        case content(project: Project?, filter: ContentFilter, scrollToItem: ChatItem? = nil)
        
        func hash(into hasher: inout Hasher) {
            switch self {
            case .content(let project, let filter, let item):
                hasher.combine(project?.id)
                hasher.combine(filter)
                hasher.combine(item?.id)
            }
        }
        
        static func == (lhs: NavigationDestination, rhs: NavigationDestination) -> Bool {
            switch (lhs, rhs) {
            case (.content(let p1, let f1, let i1), .content(let p2, let f2, let i2)):
                return p1?.id == p2?.id && f1 == f2 && i1?.id == i2?.id
            }
        }
    }
}