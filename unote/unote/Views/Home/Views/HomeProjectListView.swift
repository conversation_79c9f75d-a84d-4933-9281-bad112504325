import SwiftUI
import SwiftData

// MARK: - 主页项目列表组件
struct HomeProjectListView: View {
    let projects: [Project]
    let searchText: String
    let onEdit: (Project) -> Void
    
    @Environment(\.colorScheme) private var colorScheme
    @State private var cachedFilteredProjects: [Project] = []
    @State private var lastSearchText: String = ""
    @State private var lastProjectsCount: Int = 0
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text("mine".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundStyle(.primary)
                .padding(.horizontal, 6)
                
            LazyVStack(spacing: 0) {
                ForEach(filteredProjects) { currentProject in
                    NavigationLink(destination: ContentView(project: currentProject, filter: .project, scrollToItem: nil)) {
                        ProjectRow(project: currentProject, onEdit: onEdit)
                    }
                    
                    if currentProject != filteredProjects.last {
                        Divider()
                            .foregroundColor(.border0)
                            .opacity(colorScheme == .dark ? 0.6 : 0.5)
                            .padding(.horizontal, 4)
                    }
                }
            }
        }
        .padding(.horizontal, 16)
    }
    
    private var filteredProjects: [Project] {
        // 优化：检查缓存是否有效
        if searchText == lastSearchText && projects.count == lastProjectsCount {
            return cachedFilteredProjects
        }
        
        // 重新计算并缓存
        let filtered = if searchText.isEmpty {
            projects
        } else {
            projects.filter { $0.name.localizedCaseInsensitiveContains(searchText) }
        }
        
        let sorted = filtered.sorted { $0.latestActivityTime > $1.latestActivityTime }
        
        // 更新缓存
        DispatchQueue.main.async {
            cachedFilteredProjects = sorted
            lastSearchText = searchText
            lastProjectsCount = projects.count
        }
        
        return sorted
    }
}

// MARK: - 项目行视图
struct ProjectRow: View {
    let project: Project
    let onEdit: (Project) -> Void
    @Environment(\.colorScheme) private var colorScheme
    @Environment(\.modelContext) private var modelContext
    @State private var showDeleteAlert = false
    
    var body: some View {
        NavigationLink(destination: ContentView(project: project, filter: .project, scrollToItem: nil)) {
            HStack(spacing: 12) {
                projectAvatar
                    .frame(width: 48, height: 48)
                    .background(Color(.theme0).opacity(colorScheme == .dark ? 1 : 0.03))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .inset(by: 0.50)
                            .stroke(Color(.text0).opacity(0.05), lineWidth: 0.50)
                    )
                
                VStack(alignment: .leading, spacing: 6) {
                    HStack(spacing: 4) {
                        Text(project.name)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(.label))
                    }
                    
                    if let message = project.latestMessage {
                        Text(message)
                            .font(.system(size: 14))
                            .foregroundColor(Color(.secondaryLabel))
                            .lineLimit(1)
                    }
                }
                
                Spacer()
                
                VStack(alignment: .trailing, spacing: 8) {
                    if let todayCount = getTodayMessageCount(), todayCount > 0 {
                        Text("+\(todayCount)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 2)
                            .background(Color.theme0)
                            .cornerRadius(4)
                    }
                    
                    if project.latestMessage != nil {
                        Text(formatTime(project.latestActivityTime))
                            .font(.system(size: 13))
                            .foregroundColor(Color(.secondaryLabel))
                    }
                }
            }
            .padding(.horizontal, 4)
            .padding(.vertical, 14)
        }
        .buttonStyle(ProjectRowButtonStyle())
        .contextMenu {
            Button {
                onEdit(project)
            } label: {
                Label("edit_project".localized, systemImage: "pencil")
            }
            
            Button(role: .destructive) {
                showDeleteAlert = true
            } label: {
                Label("delete_project".localized, systemImage: "trash")
            }
        }
        .alert("delete_project_title".localized, isPresented: $showDeleteAlert) {
            Button("cancel".localized, role: .cancel) { }
            Button("delete".localized, role: .destructive) {
                deleteProject()
            }
        } message: {
            Text(String(format: "confirm_delete_project".localized, project.name))
        }
    }
    
    // 优化的今日消息数量获取
    private func getTodayMessageCount() -> Int? {
        let count = project.todayMessageCount
        return count > 0 ? count : nil
    }
    
    private var projectAvatar: some View {
        Group {
            if project.avatarType == .icon {
                Text(project.avatarName)
                    .font(.system(size: 20))
                    .foregroundColor(.primary)
            } else if let imageData = project.avatarImageData,
                      let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } else {
                // 默认图标
                Image(systemName: "folder")
                    .font(.system(size: 20))
                    .foregroundColor(.primary)
            }
        }
    }
    
    // 添加时间格式化函数
    private func formatTime(_ date: Date) -> String {
        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            return formatter.string(from: date)
        } else if calendar.isDateInYesterday(date) {
            return "yesterday_time".localized
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear) {
            let formatter = DateFormatter()
            formatter.dateFormat = "weekday_format".localized
            return formatter.string(from: date)
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "month_day_format_localized".localized
            return formatter.string(from: date)
        }
    }
    
    private func deleteProject() {
        modelContext.delete(project)
        try? modelContext.save()
    }
    

}

// MARK: - 按钮样式
struct ProjectRowButtonStyle: ButtonStyle {
    @Environment(\.colorScheme) private var colorScheme
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(
                Color(.systemBackground)
                    .opacity(configuration.isPressed ? (colorScheme == .dark ? 0.1 : 0.05) : 0)
            )
            .contentShape(Rectangle()) // 确保整个区域可点击
    }
} 