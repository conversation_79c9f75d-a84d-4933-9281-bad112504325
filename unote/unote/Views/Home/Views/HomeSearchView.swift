import SwiftUI
import SwiftData

// MARK: - 主页搜索组件
struct HomeSearchView: View {
    @Binding var searchText: String
    let searchResults: [(Project?, ChatItem)]
    @Binding var navigationPath: NavigationPath
    @State private var searchTask: Task<Void, Never>?
    @State private var isSearching = false
    
    let allChatItems: [ChatItem]
    private let maxSearchResults = 10
    
    var body: some View {
        EmptyView() // 搜索功能通过searchable修饰符实现
    }
    
    // MARK: - 搜索逻辑
    // 搜索功能已移到SearchViewModel，这里不再需要
    func triggerSearch(query: String) {
        // 搜索功能已交由SearchViewModel处理
    }
    
    nonisolated private static func calculateSearchScore(item: ChatItem, terms: [String]) -> Int {
        var score = 0
        let itemText = item.text.lowercased()
        
        for term in terms {
            if itemText.contains(term) {
                score += 3
                // 标题匹配给予更高权重
                if itemText.components(separatedBy: .newlines)
                    .first?.contains(term) == true {
                    score += 2
                }
            }
            
            // 标签匹配
            if item.tags.contains(where: { $0.lowercased().contains(term) }) {
                score += 2
            }
        }
        
        // 最近项目加分
        if Calendar.current.isDateInToday(item.timestamp) {
            score += 1
        }
        return score
    }
    
    func navigateToSearchResult(project: Project?, item: ChatItem) {
        if let project = project {
            // 导航到项目内容
            navigationPath.append(UIStateManager.NavigationDestination.content(
                project: project,
                filter: .project,
                scrollToItem: item
            ))
        } else {
            // 导航到全部内容
            navigationPath.append(UIStateManager.NavigationDestination.content(
                project: nil,
                filter: .all,
                scrollToItem: item
            ))
        }
    }
}

// MARK: - 搜索结果行视图
struct SearchResultRow: View {
    let project: Project?
    let item: ChatItem
    
    private let itemFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        return formatter
    }()
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(item.text)
                .lineLimit(1)
                .foregroundColor(.primary)
            
            HStack {
                if let project = project {
                    Text(project.name)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Text(itemFormatter.string(from: item.timestamp))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
    }
} 