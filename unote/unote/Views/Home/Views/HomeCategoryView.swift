import SwiftUI

// MARK: - 类别统计数据结构
struct CategoryStatistics {
    let allCount: Int
    let todayCount: Int
    let uncategorizedCount: Int
    let todayUncategorizedCount: Int
    let imageCount: Int
    let todayImageCount: Int
    let pomodoroCount: Int
    let todayPomodoroCount: Int
    let favoriteCount: Int
    let todayFavoriteCount: Int
    let plannedCount: Int
    let todayPlannedCount: Int
}
import SwiftData

// MARK: - 主页类别网格组件
struct HomeCategoryView: View {
    let allChatItems: [ChatItem]
    @State private var cachedStats: (date: Date, stats: CategoryStatistics)?
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        VStack(spacing: 0) {
            ForEach([
                ("all_items".localized, "magazine", allItemsCount, todayItemsCount, Color.blue)
            ], id: \.0) { title, icon, count, todayCount, tint in
                CategoryButton(
                    title: title,
                    icon: icon,
                    count: count,
                    todayCount: todayCount,
                    tint: tint,
                    destination: ContentView(
                        project: nil,
                        filter: title == "all_items".localized ? .all : 
                               title == "planned_items".localized ? .planned : .favorites,
                        scrollToItem: nil
                    )
                )
            }
        }
        .padding(.horizontal, 16)
    }
    
    // MARK: - 优化的统计计算
    private var categoryStats: CategoryStatistics {
        let today = Calendar.current.startOfDay(for: Date())
        
        // 检查缓存
        if let cached = cachedStats, Calendar.current.isDate(cached.date, inSameDayAs: today) {
            return cached.stats
        }
        
        // 一次性计算所有统计
        var allCount = 0
        var todayCount = 0
        var uncategorizedCount = 0
        var todayUncategorizedCount = 0
        var imageCount = 0
        var todayImageCount = 0
        var pomodoroCount = 0
        var todayPomodoroCount = 0
        var favoriteCount = 0
        var todayFavoriteCount = 0
        var plannedCount = 0
        var todayPlannedCount = 0
        
        for item in allChatItems {
            allCount += 1
            
            let isToday = Calendar.current.isDateInToday(item.timestamp)
            if isToday {
                todayCount += 1
            }
            
            if item.project == nil {
                uncategorizedCount += 1
                if isToday {
                    todayUncategorizedCount += 1
                }
            }
            
            if !item.imageData.isEmpty {
                imageCount += 1
                if isToday {
                    todayImageCount += 1
                }
            }
            
            if (item.pomodoroCount ?? 0) > 0 || item.completedPomodoros > 0 {
                pomodoroCount += 1
                if isToday {
                    todayPomodoroCount += 1
                }
            }
            
            if item.isFavorite {
                favoriteCount += 1
                if isToday {
                    todayFavoriteCount += 1
                }
            }
            
            if item.plannedDate != nil {
                plannedCount += 1
                if let plannedDate = item.plannedDate, Calendar.current.isDateInToday(plannedDate) {
                    todayPlannedCount += 1
                }
            }
        }
        
        let stats = CategoryStatistics(
            allCount: allCount,
            todayCount: todayCount,
            uncategorizedCount: uncategorizedCount,
            todayUncategorizedCount: todayUncategorizedCount,
            imageCount: imageCount,
            todayImageCount: todayImageCount,
            pomodoroCount: pomodoroCount,
            todayPomodoroCount: todayPomodoroCount,
            favoriteCount: favoriteCount,
            todayFavoriteCount: todayFavoriteCount,
            plannedCount: plannedCount,
            todayPlannedCount: todayPlannedCount
        )
        
        // 延迟缓存以避免在视图更新期间修改状态
        DispatchQueue.main.async {
            self.cachedStats = (today, stats)
        }
        return stats
    }
    
    // 兼容性属性（避免破坏现有代码）
    private var allItemsCount: Int { categoryStats.allCount }
    private var todayItemsCount: Int { categoryStats.todayCount }
    private var uncategorizedItemsCount: Int { categoryStats.uncategorizedCount }
    private var todayUncategorizedCount: Int { categoryStats.todayUncategorizedCount }
    private var imageItemsCount: Int { categoryStats.imageCount }
    private var todayImageCount: Int { categoryStats.todayImageCount }
    private var pomodoroItemsCount: Int { categoryStats.pomodoroCount }
    private var todayPomodoroCount: Int { categoryStats.todayPomodoroCount }
    private var favoriteItemsCount: Int { categoryStats.favoriteCount }
    private var todayFavoriteCount: Int { categoryStats.todayFavoriteCount }
    private var plannedItemsCount: Int { categoryStats.plannedCount }
    private var todayPlannedCount: Int { categoryStats.todayPlannedCount }
}

// MARK: - 类别按钮组件 (4.01版本原始设计)
struct CategoryButton<Destination: View>: View {
    @Environment(\.colorScheme) private var colorScheme
    @Query private var allChatItems: [ChatItem]
    
    let title: String
    let icon: String
    let count: Int
    let todayCount: Int
    let tint: Color
    let destination: Destination
    
    init(
        title: String,
        icon: String,
        count: Int,
        todayCount: Int,
        tint: Color,
        destination: Destination
    ) {
        self.title = title
        self.icon = icon
        self.count = count
        self.todayCount = todayCount
        self.tint = tint
        self.destination = destination
    }
    
    var body: some View {
        NavigationLink(destination: destination) {
            HStack(spacing: 12) {
                // 左侧图标
                Image(systemName: icon)
                    .font(.system(size: 20))
                    .fontWeight(.medium)
                    .foregroundColor(.primary.opacity(0.85))
                    .frame(width: 48, height: 48)
                    .background(Color(.theme0).opacity(colorScheme == .dark ? 1 : 0.03))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .inset(by: 0.50)
                            .stroke(Color(.text0).opacity(0.05), lineWidth: 0.50)
                    )
                
                VStack(alignment: .leading, spacing: 6) {
                    // 标题行
                    HStack(spacing: 4) {
                        Text(title)
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(Color(.label))
                        
                        Text("\(count)")
                            .font(.system(size: 16))
                            .foregroundColor(Color(.secondaryLabel))
                    }
                }
                
                Spacer()
                
                // 右侧时间和计数
                VStack(alignment: .trailing, spacing: 8) {
                    if todayCount > 0 {
                        Text("+\(todayCount)")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 4)
                            .padding(.vertical, 2)
                            .background(Color.theme0)
                            .cornerRadius(4)
                    }
                }
            }
            .padding(.horizontal, 4)
            .padding(.vertical, 14)
        }
        .buttonStyle(CategoryButtonStyle())
    }
}

// MARK: - 类别按钮样式 (4.01版本原始样式)
struct CategoryButtonStyle: ButtonStyle {
    @Environment(\.colorScheme) private var colorScheme
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .background(
                Color(.systemBackground)
                    .opacity(configuration.isPressed ? (colorScheme == .dark ? 0.1 : 0.05) : 0)
            )
            .contentShape(Rectangle())
    }
}