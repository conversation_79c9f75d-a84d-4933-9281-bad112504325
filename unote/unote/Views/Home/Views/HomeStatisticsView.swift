import SwiftUI
import SwiftData

// MARK: - 统计数据结构
struct TodayStatistics {
    let taskCount: Int
    let noteCount: Int
    let income: Double
    let expense: Double
}

// MARK: - 主页统计组件
struct HomeStatisticsView: View {
    let allChatItems: [ChatItem]
    @Binding var showingStats: Bool
    
    @Environment(\.colorScheme) private var colorScheme
    @State private var cachedStats: (date: Date, stats: TodayStatistics)?
    
    // 共享的DateFormatter
    private static let dateFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.locale = LocalizationManager.shared.currentLocale
        return formatter
    }()
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(dateText)
                .font(.system(size: 16, weight: .bold))
            Text(statisticsText)
                .font(.system(size: 14))
                .foregroundColor(.secondary)
        }
        .padding(16)
        .frame(maxWidth: .infinity, alignment: .leading)
        .background(Color(.theme0).opacity(colorScheme == .dark ? 0.7 : 0.03))
        .cornerRadius(10)
        .onTapGesture {
            showingStats = true
        }
    }
    
    // MARK: - 计算属性
    private var dateText: String {
        Self.dateFormatter.dateFormat = "month_day_format_localized".localized + " " + "weekday_format".localized
        return Self.dateFormatter.string(from: Date())
    }
    
    private var statisticsText: String {
        let stats = getTodayStatistics()
        var components: [String] = []
        
        // 添加笔记统计
        if stats.noteCount > 0 {
            components.append(String(format: "notes_count".localized, stats.noteCount))
        }
        
        // 添加任务统计
        if stats.taskCount > 0 {
            components.append(String(format: "tasks_count".localized, stats.taskCount))
        }
        
        // 添加收入统计
        if stats.income > 0 {
            components.append(String(format: "income_amount".localized, stats.income))
        }
        
        // 添加支出统计
        if stats.expense > 0 {
            components.append(String(format: "expense_amount".localized, stats.expense))
        }
        
        // 如果没有任何统计项，返回默认文本
        if components.isEmpty {
            return "no_activities_today".localized
        }
        
        // 用逗号连接所有统计项
        return components.joined(separator: ", ")
    }
    
    // 优化：一次性计算所有今日统计，避免重复过滤
    private func getTodayStatistics() -> TodayStatistics {
        let today = Calendar.current.startOfDay(for: Date())
        
        // 检查缓存
        if let cached = cachedStats, Calendar.current.isDate(cached.date, inSameDayAs: today) {
            return cached.stats
        }
        
        // 一次性过滤出今日所有项目
        let todayItems = allChatItems.filter { 
            Calendar.current.isDateInToday($0.effectiveTimestamp)
        }
        
        var taskCount = 0
        var noteCount = 0
        var income: Double = 0
        var expense: Double = 0
        
        // 一次遍历计算所有统计
        for item in todayItems {
            switch item.type {
            case .task:
                taskCount += 1
            case .note:
                noteCount += 1
            case .expense:
                // 支出类型不计入笔记或任务统计
                break
            }
            
            if let amount = item.amount, amount != 0 {
                if amount > 0 {
                    income += amount
                } else {
                    expense += abs(amount)
                }
            }
        }
        
        let stats = TodayStatistics(
            taskCount: taskCount,
            noteCount: noteCount,
            income: income,
            expense: expense
        )
        
        // 延迟缓存以避免在视图更新期间修改状态
        DispatchQueue.main.async {
            self.cachedStats = (today, stats)
        }
        return stats
    }
}

// MARK: - 问候语组件
struct HomeGreetingView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(greetingText)
                .font(.system(size: 28, weight: .bold))
                .foregroundColor(.primary)
            
            Text(dateText)
                .font(.system(size: 17))
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 20)
        .padding(.top, 16)
    }
    
    private var greetingText: String {
        let hour = Calendar.current.component(.hour, from: Date())
        switch hour {
        case 0..<6:
            return "late_night_greeting".localized
        case 6..<9:
            return "good_morning".localized
        case 9..<12:
            return "good_forenoon".localized
        case 12..<14:
            return "good_noon".localized
        case 14..<18:
            return "good_afternoon".localized
        case 18..<22:
            return "good_evening".localized
        default:
            return "good_night".localized
        }
    }

    private var dateText: String {
        let formatter = DateFormatter()
        formatter.locale = LocalizationManager.shared.currentLocale
        formatter.dateFormat = "month_day_format_localized".localized + " " + "weekday_format".localized
        return formatter.string(from: Date())
    }
} 
