import SwiftUI

struct CustomNavigationController<Content: View>: UIViewControllerRepresentable {
    let content: Content
    @Binding var isInteractivePopGestureEnabled: Bool
    
    func makeUIViewController(context: Context) -> UINavigationController {
        let navigationController = UINavigationController(rootViewController: UIHostingController(rootView: content))
        navigationController.navigationBar.isHidden = true
        return navigationController
    }
    
    func updateUIViewController(_ uiViewController: UINavigationController, context: Context) {
        uiViewController.interactivePopGestureRecognizer?.isEnabled = isInteractivePopGestureEnabled
    }
}