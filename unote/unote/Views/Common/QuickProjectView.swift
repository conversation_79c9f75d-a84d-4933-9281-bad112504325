import SwiftUI
import SwiftData

struct QuickProjectView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Environment(\.colorScheme) private var colorScheme
    @Binding var selectedCategory: ProjectCategory
    @State private var isCreating = false
    @State private var showSuccessAlert = false
    @State private var errorMessage = ""
    @State private var showErrorAlert = false
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 说明文字
                    VStack(spacing: 8) {
                        Text("quick_create_description".localized)
                            .font(.system(size: 16))
                            .foregroundColor(Color(.secondaryLabel))
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal, 20)
                    .padding(.top, 10)
                    
                    // 场景分类
                    ForEach(Array(ProjectCategory.scenes.keys.sorted()), id: \.self) { scene in
                        SceneSection(
                            scene: scene,
                            categories: ProjectCategory.scenes[scene] ?? [],
                            isCreating: isCreating,
                            onCreateProject: createProject
                        )
                    }
                }
                .padding(.horizontal, 16)
                .padding(.bottom, 20)
            }
            .navigationTitle("quick_create_title".localized)
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .cancellationAction) {
                    Button("cancel".localized) { dismiss() }
                }
            }
            .alert("success_title".localized, isPresented: $showSuccessAlert) {
                Button("ok".localized) { dismiss() }
            } message: {
                Text("project_created_success".localized)
            }
            .alert("error".localized, isPresented: $showErrorAlert) {
                Button("confirm".localized, role: .cancel) { }
            } message: {
                Text(errorMessage)
            }
        }
    }
    
    private func createProject(category: ProjectCategory) {
        guard !isCreating else { return }
        isCreating = true
        
        // 修改谓词的写法
        let categoryPrefix = category.rawValue
        let descriptor = FetchDescriptor<Project>(
            predicate: #Predicate<Project> { project in
                project.name.starts(with: categoryPrefix)
            }
        )
        
        do {
            let existingProjects = try modelContext.fetch(descriptor)
            let projectCount = existingProjects.count
            
            // 生成项目名称，不带数字如果已存在则添加数字
            var projectName = category.rawValue
            if projectCount > 0 {
                projectName = "\(category.rawValue) \(projectCount + 1)"
            }
            
            let newProject = Project(
                name: projectName,
                avatarType: .icon,
                avatarName: category.icon
            )
            
            modelContext.insert(newProject)
            try modelContext.save()
            
            selectedCategory = category
            
            // 显示成功提示
            withAnimation {
                showSuccessAlert = true
            }
            
            // 延迟关闭
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                dismiss()
            }
            
        } catch {
            errorMessage = String(format: "create_project_failed".localized, error.localizedDescription)
            showErrorAlert = true
            isCreating = false
        }
    }
}

// MARK: - 场景分组
struct SceneSection: View {
    let scene: String
    let categories: [ProjectCategory]
    let isCreating: Bool
    let onCreateProject: (ProjectCategory) -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 场景标题
            Text(scene)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(Color(.label))
                .padding(.horizontal, 4)
            
            // 分类网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
                ForEach(categories, id: \.self) { category in
                    CategoryCard(
                        category: category,
                        isCreating: isCreating
                    ) {
                        onCreateProject(category)
                    }
                }
            }
        }
    }
}

// MARK: - 分类卡片
struct CategoryCard: View {
    let category: ProjectCategory
    let isCreating: Bool
    let action: () -> Void
    
    @Environment(\.colorScheme) private var colorScheme
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 12) {
                // 图标
                Text(category.icon)
                    .font(.system(size: 32))
                    .frame(width: 60, height: 60)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .inset(by: 0.50)
                            .stroke(Color(.separator), lineWidth: 0.50)
                    )
                
                // 标题
                Text(category.rawValue)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(Color(.label))
                    .lineLimit(1)
                
                // 创建按钮
                HStack(spacing: 4) {
                    if isCreating {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: Color.theme0))
                            .scaleEffect(0.7)
                    } else {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 12, weight: .medium))
                    }
                    
                    Text(isCreating ? "creating_project".localized : "create".localized)
                        .font(.system(size: 12, weight: .medium))
                }
                .foregroundColor(Color.theme0)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 16)
            .background(Color(.systemBackground))
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .inset(by: 0.50)
                    .stroke(Color(.separator), lineWidth: 0.50)
            )
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isCreating)
    }
}
