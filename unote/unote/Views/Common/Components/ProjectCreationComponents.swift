import SwiftUI
import SwiftData

// MARK: - Project Creation Components
// 项目创建相关的UI组件，遵循极简主义设计原则

/// 快速创建卡片
struct QuickCreateCard: View {
    @Binding var showQuickCreate: Bool
    
    var body: some View {
        Button(action: { showQuickCreate = true }) {
            HStack(spacing: 12) {
                // 左侧图标
                Image(systemName: "bolt.fill")
                    .font(.title2)
                    .foregroundColor(.white)
                    .frame(width: 40, height: 40)
                    .background(
                        LinearGradient(
                            gradient: Gradient(colors: [.blue, .purple]),
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .cornerRadius(12)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("quick_create_title".localized)
                        .font(.headline)
                        .foregroundColor(.primary)
                    
                    Text("quick_create_subtitle".localized)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(16)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 自定义创建卡片
struct CustomCreateCard: View {
    @Bindable var manager: ProjectCreationManager
    @Binding var isEmojiPickerPresented: Bool
    @Binding var isImagePickerPresented: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            // 头部
            HStack {
                Text("custom_create_title".localized)
                    .font(.headline)
                    .foregroundColor(.primary)
                
                Spacer()
            }
            
            // 项目名称输入
            ProjectNameInputView(manager: manager)
            
            // 头像选择
            AvatarSelectionView(
                manager: manager,
                isEmojiPickerPresented: $isEmojiPickerPresented,
                isImagePickerPresented: $isImagePickerPresented
            )
            
            // 分类选择
            CategorySelectionView(manager: manager)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .shadow(color: .black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

/// 项目名称输入组件
struct ProjectNameInputView: View {
    @Bindable var manager: ProjectCreationManager
    @Environment(\.modelContext) private var modelContext
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("project_name_label".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            TextField("project_name_placeholder".localized, text: $manager.projectName)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .onChange(of: manager.projectName) { _, _ in
                    manager.validateProjectName(with: modelContext)
                }
            
            if let error = manager.nameError {
                Text(error)
                    .font(.caption)
                    .foregroundColor(.red)
                    .transition(.opacity)
            }
        }
    }
}

/// 头像选择组件
struct AvatarSelectionView: View {
    @Bindable var manager: ProjectCreationManager
    @Binding var isEmojiPickerPresented: Bool
    @Binding var isImagePickerPresented: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("avatar_selection_label".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            // 头像类型选择
            Picker("avatar_type".localized, selection: $manager.avatarType) {
                ForEach(AvatarType.allCases, id: \.self) { type in
                    Label(type.displayName, systemImage: type.systemImage)
                        .tag(type)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            
            // 头像预览和选择
            HStack(spacing: 16) {
                // 头像预览
                AvatarPreviewView(manager: manager)
                
                // 选择按钮
                Button(action: {
                    if manager.avatarType == .icon {
                        isEmojiPickerPresented = true
                    } else {
                        isImagePickerPresented = true
                    }
                }) {
                    Text(manager.avatarType == .icon ? "select_emoji".localized : "select_image".localized)
                        .font(.subheadline)
                        .foregroundColor(.blue)
                }
            }
        }
    }
}

/// 头像预览组件
struct AvatarPreviewView: View {
    @Bindable var manager: ProjectCreationManager
    
    var body: some View {
        Group {
            if manager.avatarType == .icon {
                Text(manager.avatarName)
                    .font(.system(size: 32))
                    .frame(width: 60, height: 60)
                    .background(Color(.systemGray6))
                    .cornerRadius(12)
            } else {
                if let image = manager.selectedImages.first {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: 60, height: 60)
                        .clipped()
                        .cornerRadius(12)
                } else {
                    Image(systemName: "photo")
                        .font(.title2)
                        .foregroundColor(.secondary)
                        .frame(width: 60, height: 60)
                        .background(Color(.systemGray6))
                        .cornerRadius(12)
                }
            }
        }
    }
}

/// 分类选择组件
struct CategorySelectionView: View {
    @Bindable var manager: ProjectCreationManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("category_selection_label".localized)
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.primary)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 3), spacing: 8) {
                ForEach(ProjectCategory.allCases, id: \.self) { category in
                    ProjectCategoryButton(
                        category: category,
                        isSelected: manager.selectedCategory == category
                    ) {
                        manager.selectedCategory = category
                    }
                }
            }
        }
    }
}

/// 项目分类按钮组件
struct ProjectCategoryButton: View {
    let category: ProjectCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Text(category.icon)
                    .font(.title2)
                
                Text(category.displayName)
                    .font(.caption2)
                    .lineLimit(1)
            }
            .frame(maxWidth: .infinity)
            .padding(.vertical, 8)
            .background(
                isSelected ? category.color.opacity(0.2) : Color(.systemGray6)
            )
            .foregroundColor(
                isSelected ? category.color : .primary
            )
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(
                        isSelected ? category.color : Color.clear,
                        lineWidth: 2
                    )
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 快速创建模板列表
struct QuickCreateTemplateList: View {
    let onSelect: (QuickCreateTemplate) -> Void
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 12), count: 2), spacing: 12) {
            ForEach(QuickCreateTemplate.templates, id: \.name) { template in
                QuickCreateTemplateCard(template: template, onSelect: onSelect)
            }
        }
    }
}

/// 快速创建模板卡片
struct QuickCreateTemplateCard: View {
    let template: QuickCreateTemplate
    let onSelect: (QuickCreateTemplate) -> Void
    
    var body: some View {
        Button(action: { onSelect(template) }) {
            VStack(spacing: 12) {
                Text(template.emoji)
                    .font(.system(size: 32))
                
                Text(template.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.primary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(template.category.color.opacity(0.1))
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(template.category.color.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

/// 创建按钮组件
struct CreateProjectButton: View {
    @Bindable var manager: ProjectCreationManager
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                if manager.isCreating {
                    ProgressView()
                        .scaleEffect(0.8)
                        .foregroundColor(.white)
                } else {
                    Image(systemName: "plus")
                        .font(.headline)
                }
                
                Text(manager.isCreating ? "creating_project".localized : "create_project".localized)
                    .font(.headline)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                manager.canCreate ? 
                    LinearGradient(
                        gradient: Gradient(colors: [.blue, .purple]),
                        startPoint: .leading,
                        endPoint: .trailing
                    ) :
                    LinearGradient(
                        gradient: Gradient(colors: [.gray, .gray]),
                        startPoint: .leading,
                        endPoint: .trailing
                    )
            )
            .cornerRadius(16)
        }
        .disabled(!manager.canCreate)
        .buttonStyle(PlainButtonStyle())
    }
}
