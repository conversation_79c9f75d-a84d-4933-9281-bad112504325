import SwiftUI

struct TagManagementView: View {
    @ObservedObject var tagManager: TagManager
    @Binding var selectedTags: [String]
    @Environment(\.dismiss) private var dismiss
    @State private var newTag = ""
    @State private var editMode: EditMode = .inactive
    var onTagSelect: (String) -> Void
    
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("add_new_tag_section".localized)) {
                    HStack {
                        TextField("enter_new_tag".localized, text: $newTag)
                        Button(action: {
                            addNewTag()
                        }) {
                            Text("add_button".localized)
                        }
                        .disabled(newTag.isEmpty)
                    }
                }
                
                Section(header: Text("common_tags_section".localized)) {
                    ForEach(tagManager.frequentTags) { tagInfo in
                        Button(action: {
                            toggleTag(tagInfo.tag)
                        }) {
                            HStack {
                                Text(tagInfo.tag)
                                Spacer()
                                if selectedTags.contains(tagInfo.tag) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                }
                                Text(String(format: "usage_count_format".localized, tagInfo.count))
                                    .foregroundColor(.gray)
                                    .font(.caption)
                            }
                        }
                        .foregroundColor(.primary)
                    }
                    .onDelete(perform: deleteFrequentTag)
                }
                
                Section(header: Text("recent_tags_section".localized)) {
                    ForEach(tagManager.recentTags, id: \.self) { tag in
                        Button(action: {
                            toggleTag(tag)
                        }) {
                            HStack {
                                Text(tag)
                                Spacer()
                                if selectedTags.contains(tag) {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(.blue)
                                }
                            }
                        }
                        .foregroundColor(.primary)
                    }
                    .onDelete(perform: deleteRecentTag)
                }
            }
            .navigationTitle("manage_tags_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("complete_button".localized) {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    EditButton()
                }
            }
            .environment(\.editMode, $editMode)
        }
    }
    
    private func toggleTag(_ tag: String) {
        if selectedTags.contains(tag) {
            selectedTags.removeAll { $0 == tag }
        } else {
            selectedTags.append(tag)
            tagManager.addTag(tag) // 更新使用次数
        }
    }
    
    private func addNewTag() {
        let trimmedTag = newTag.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTag.isEmpty else { return }
        
        tagManager.addTag(trimmedTag)
        if !selectedTags.contains(trimmedTag) {
            selectedTags.append(trimmedTag)
        }
        newTag = ""
    }
    
    private func deleteFrequentTag(at offsets: IndexSet) {
        tagManager.frequentTags.remove(atOffsets: offsets)
        tagManager.saveTags()
    }
    
    private func deleteRecentTag(at offsets: IndexSet) {
        tagManager.recentTags.remove(atOffsets: offsets)
        tagManager.saveTags()
    }
}
