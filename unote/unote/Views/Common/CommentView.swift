import SwiftUI
import SwiftData

struct CommentView: View {
    let comment: Comment
    var onEdit: (String) -> Void
    var onDelete: () -> Void
    @State private var isEditing = false
    @State private var editedText: String = ""
    @State private var showingActionSheet = false
    
    var body: some View {
        if isEditing {
            HStack(spacing: 8) {
                TextField("enter_comment".localized, text: $editedText)
                    .textFieldStyle(.roundedBorder)
                    .font(.system(size: 14))
                
                Button("save".localized) {
                    onEdit(editedText)
                    isEditing = false
                }
                .foregroundColor(.blue)
                .font(.caption)
                
                Button("cancel".localized) {
                    isEditing = false
                }
                .foregroundColor(.gray)
                .font(.caption)
            }
            .padding(.vertical, 4)
        } else {
            HStack(alignment: .bottom, spacing: 8) {
                Text(comment.text)
                    .font(.system(size: 14))
                    .fixedSize(horizontal: false, vertical: true)
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
                
                Spacer()
                
                Text(formatDate(comment.timestamp))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.vertical, 4)
            .contentShape(Rectangle()) // 确保整个区域可点击
            .onLongPressGesture {
                showingActionSheet = true
            }
            .confirmationDialog("comment_actions".localized, isPresented: $showingActionSheet, titleVisibility: .hidden) {
                Button("edit".localized) {
                    editedText = comment.text
                    isEditing = true
                }
                
                Button("delete".localized, role: .destructive) {
                    onDelete()
                }
                
                Button("cancel".localized, role: .cancel) {}
            }
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let now = Date()
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            let formatter = DateFormatter()
            formatter.dateFormat = "today_format".localized
            return formatter.string(from: date)
        } else if calendar.isDateInYesterday(date) {
            let formatter = DateFormatter()
            formatter.dateFormat = "yesterday_format".localized
            return formatter.string(from: date)
        } else if calendar.component(.year, from: date) == calendar.component(.year, from: now) {
            let formatter = DateFormatter()
            formatter.dateFormat = "this_year_format".localized
            return formatter.string(from: date)
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "other_year_format".localized
            return formatter.string(from: date)
        }
    }
}
