import SwiftUI
import SwiftData

struct ProjectDetailView: View {
    @Bindable var project: Project
    @State private var newMessage = ""
    
    var body: some View {
        VStack {
            List {
                ForEach(project.chatItems ?? []) { item in
                    ChatItemRow(chatItem: item)
                }
            }
            
            HStack {
                TextField("enter_new_message".localized, text: $newMessage)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                
                Button(action: sendMessage) {
                    Image(systemName: "paperplane.fill")
                }
            }
            .padding()
        }
        .navigationTitle(project.name)
        .navigationBarItems(trailing: But<PERSON>(action: {
            // 实现编辑项目的逻辑
        }) {
            Image(systemName: "pencil")
        })
    }
    
    private func sendMessage() {
        guard !newMessage.isEmpty else { return }
        let newItem = ChatItem(text: newMessage, type: .note, project: project)
        project.chatItems?.append(newItem)
        project.lastUpdated = Date()
        newMessage = ""
    }
}

struct ChatItemRow: View {
    let chatItem: ChatItem
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            Text(chatItem.text)
                .padding()
                .background(Color.gray.opacity(0.2))
                .cornerRadius(10)
            
            Text(chatItem.timestamp, style: .time)
                .font(.caption)
                .foregroundColor(.gray)
        }
    }
}