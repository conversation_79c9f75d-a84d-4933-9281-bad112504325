import SwiftUI

struct MediaPickerView: View {
    var body: some View {
        HStack {
            But<PERSON>(action: {
                // 这里添加选择媒体的逻辑
            }) {
                Image(systemName: "paperclip")
                    .foregroundColor(.blue)
            }
            
            Button(action: {
                // 这里添加拍照的逻辑
            }) {
                Image(systemName: "camera")
                    .foregroundColor(.blue)
            }
        }
    }
}
