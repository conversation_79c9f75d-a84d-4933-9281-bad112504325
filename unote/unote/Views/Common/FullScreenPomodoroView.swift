import SwiftUI
import SwiftData

struct FullScreenPomodoroView: View {
    @EnvironmentObject var pomodoroManager: PomodoroManager
    let taskTitle: String
    let onClose: () -> Void
    let totalPomodoros: Int
    
    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)
            
            VStack {
                Text(taskTitle)
                    .font(.title2)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                
                Text("\(pomodoroManager.completedPomodoros)/\(totalPomodoros)")
                    .font(.title3)
                    .foregroundColor(.white)
                    .padding(.top, 10)
                
                Spacer()
                
                ZStack {
                    Circle()
                        .stroke(lineWidth: 20)
                        .opacity(0.3)
                        .foregroundColor(.gray)
                    
                    Circle()
                        .trim(from: 0.0, to: CGFloat(pomodoroManager.remainingTime) / (25 * 60))
                        .stroke(style: StrokeStyle(lineWidth: 20, lineCap: .round, lineJoin: .round))
                        .foregroundColor(.white)
                        .rotationEffect(Angle(degrees: 270.0))
                        .animation(.linear, value: pomodoroManager.remainingTime)
                    
                    Text(timeString(from: pomodoroManager.remainingTime))
                        .font(.system(size: 70, weight: .thin))
                        .foregroundColor(.white)
                }
                .frame(width: 256, height: 256)
                
                Spacer()
                
                HStack(spacing: 50) {
                    Button(action: {
                        if pomodoroManager.pomodoroStatus == .running {
                            pomodoroManager.pausePomodoro()
                        } else {
                            pomodoroManager.resumePomodoro()
                        }
                    }) {
                        Image(systemName: pomodoroManager.pomodoroStatus == .running ? "pause.fill" : "play.fill")
                            .font(.title)
                            .foregroundColor(.white)
                    }
                    
                    Button(action: {
                        pomodoroManager.stopPomodoro()
                        onClose()
                    }) {
                        Image(systemName: "stop.fill")
                            .font(.title)
                            .foregroundColor(.white)
                    }

                    Button(action: onClose) {
                        Image(systemName: "xmark")
                            .font(.title2)
                            .foregroundColor(.white)
                    }
                }
                .padding(.bottom, 50)
            }
        }
        .statusBar(hidden: true)
    }
    
    private func timeString(from seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
}