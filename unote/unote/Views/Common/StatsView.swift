import SwiftUI
import SwiftData
import Charts

struct StatsView: View {
    @Binding var selectedDate: Date
    @Query private var items: [ChatItem]
    @Environment(\.dismiss) private var dismiss
    @State private var selectedTimeFrame: TimeFrame = .day
    @State private var showingDetailView = false
    @State private var selectedDetailItem: ChatItem?
    let project: Project?

    init(selectedDate: Binding<Date>, project: Project?) {
        _selectedDate = selectedDate
        self.project = project
        
        let predicate: Predicate<ChatItem>
        if let projectId = project?.id {
            predicate = #Predicate<ChatItem> { item in
                item.project?.id == projectId
            }
        } else {
            predicate = #Predicate<ChatItem> { _ in true }
        }
        
        _items = Query(filter: predicate, sort: \ChatItem.timestamp, order: .reverse)
    }

    enum TimeFrame: String, CaseIterable {
        case day = "today_range"
        case week = "this_week_range"
        case month = "this_month_range"
        case year = "this_year_range"
        
        var localizedString: String {
            return self.rawValue.localized
        }
    }
    
    var body: some View {
        NavigationStack {
            ScrollView {
                VStack(spacing: 20) {
                    timeFramePicker
                    
                    if filteredItems.isEmpty {
                        Spacer()
                        EmptyStateView()
                        Spacer()
                    } else {
                        overviewSection
                        taskSection
                        noteSection
                        pomodoroSection
                        financeSection
                    }
                }
                .padding()
            }
            .navigationTitle(project?.name ?? "stats_navigation_title".localized)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("close_button".localized) { dismiss() }
                }
            }
        }
        .sheet(isPresented: $showingDetailView) {
            if let item = selectedDetailItem {
                DetailView(item: item)
            }
        }
    }
    
    private var timeFramePicker: some View {
        Picker("time_range_picker".localized, selection: $selectedTimeFrame) {
            ForEach(TimeFrame.allCases, id: \.self) { timeFrame in
                Text(timeFrame.localizedString).tag(timeFrame)
            }
        }
        .pickerStyle(.segmented)
    }
    
    private var overviewSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("overview_title".localized)
                .font(.headline)
            
            VStack(spacing: 8) {
                HStack(spacing: 8) {
                    StatCard(title: "task_title".localized, value: "\(completedTaskCount)/\(taskCount)", icon: "checkmark.circle", color: .blue)
                    StatCard(title: "pomodoro_title".localized, value: "\(completedPomodoros)/\(plannedPomodoros)", icon: "timer", color: .orange)
                }
                HStack(spacing: 8) {
                    StatCard(title: "note_title".localized, value: String(format: "notes_count".localized, notesCount), icon: "note.text", color: .green)
                    StatCard(
                        title: "income_expense_situation".localized, 
                        value: String(format: "%@%.2f", "currency_symbol".localized, totalIncome - totalExpenses), 
                        icon: "dollarsign.circle", 
                        color: .purple
                    )
                }
            }
        }
    }
    
    
    private var taskSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("tasks_title".localized)
                .font(.headline)
            
            HStack(spacing: 8) {
                StatCard(title: "completion_rate_title".localized, value: String(format: "completion_percentage".localized, completionRate * 100), icon: "chart.bar.fill", color: .blue)
                StatCard(title: "incomplete_title".localized, value: String(format: "pending_tasks".localized, taskCount - completedTaskCount), icon: "exclamationmark.circle", color: .red)
            }
            
            ProgressChart(tasks: completionRate, pomodoros: pomodoroCompletionRate)
                .frame(height: 200)
            
            RecentTasksList(tasks: Array(filteredItems.filter { $0.type == .task }.prefix(3))) { item in
                selectedDetailItem = item
                showingDetailView = true
            }
        }
    }
    
    private var pomodoroSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("pomodoro_section_stats".localized)
                .font(.headline)
            
            HStack(spacing: 8) {
                StatCard(title: "completion_rate_title".localized, value: String(format: "completion_percentage".localized, pomodoroCompletionRate * 100), icon: "chart.bar.fill", color: .blue)
                StatCard(title: "total_focus_time".localized, value: formatTotalFocusTime(), icon: "clock", color: .green)
            }
            
            PomodoroTrendChart(data: generatePomodoroTrendData())
                .frame(height: 200)
        }
    }
    
    private var financeSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("finance_title".localized)
                .font(.headline)
            
            HStack(spacing: 8) {
                StatCard(title: "income_title".localized, value: String(format: "%@%.2f", "currency_symbol".localized, totalIncome), icon: "arrow.up.circle", color: .red)
                StatCard(title: "expense_title".localized, value: String(format: "%@%.2f", "currency_symbol".localized, totalExpenses), icon: "arrow.down.circle", color: .green)
            }
            
            IncomeExpenseChart(income: totalIncome, expense: totalExpenses)
                .frame(height: 200)
            
            RecentTransactionsList(transactions: Array(filteredItems.filter { $0.amount != nil }.prefix(3)))
        }
    }
    
    private var noteSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("notes_title".localized)
                .font(.headline)
            
            HStack(spacing: 8) {
                StatCard(title: "new_notes".localized, value: "\(newNotesCount)", icon: "plus.circle", color: .blue)
                StatCard(title: "total_words".localized, value: "\(totalWordsCount)", icon: "textformat", color: .green)
            }
            
            RecentNotesList(notes: Array(filteredItems.filter { $0.type == .note }.prefix(3)))
        }
    }
    
    // 计算属性
    private var filteredItems: [ChatItem] {
        items.filter { item in
            isItemInSelectedTimeFrame(item)
        }
    }
    
    private var taskCount: Int {
        filteredItems.filter { $0.type == .task }.count
    }
    
    private var completedTaskCount: Int {
        filteredItems.filter { $0.type == .task && $0.completed }.count
    }
    
    private var completionRate: Double {
        guard taskCount > 0 else { return 0 }
        return Double(completedTaskCount) / Double(taskCount)
    }
    
    private var plannedPomodoros: Int {
        filteredItems.reduce(0) { $0 + ($1.pomodoroCount ?? 0) }
    }
    
    private var completedPomodoros: Int {
        filteredItems.reduce(0) { $0 + $1.completedPomodoros }
    }
    
    private var pomodoroCompletionRate: Double {
        guard plannedPomodoros > 0 else { return 0 }
        return min(Double(completedPomodoros) / Double(plannedPomodoros), 1.0)
    }
    
    private var notesCount: Int {
        filteredItems.filter { $0.type == .note }.count
    }
    
    private var newNotesCount: Int {
        filteredItems.filter { $0.type == .note }.count
    }
    
    private var totalWordsCount: Int {
        filteredItems.filter { $0.type == .note }.reduce(0) { $0 + wordCount($1.text) }
    }
    
    private func wordCount(_ text: String) -> Int {
        do {
            let chineseRegex = try NSRegularExpression(pattern: "\\p{Han}")
            let chineseCount = chineseRegex.numberOfMatches(in: text, range: NSRange(text.startIndex..., in: text))
            
            let otherWords = text.components(separatedBy: .whitespacesAndNewlines)
                .filter { !$0.isEmpty }
                .count
            
            return chineseCount + otherWords
        } catch {
            // 如果正则表达式创建失败，回退到简单的单词计数
            return text.components(separatedBy: .whitespacesAndNewlines)
                .filter { !$0.isEmpty }
                .count
        }
    }
    
    private var totalIncome: Double {
        filteredItems.filter { $0.amount ?? 0 > 0 }.reduce(0) { $0 + ($1.amount ?? 0) }
    }
    
    private var totalExpenses: Double {
        filteredItems.filter { $0.amount ?? 0 < 0 }.reduce(0) { $0 + abs($1.amount ?? 0) }
    }
    
    private func isItemInSelectedTimeFrame(_ item: ChatItem) -> Bool {
        let calendar = Calendar.current
        let itemDate = item.effectiveTimestamp
        
        switch selectedTimeFrame {
        case .day:
            return calendar.isDate(itemDate, inSameDayAs: selectedDate)
        case .week:
            let weekRange = calendar.dateInterval(of: .weekOfYear, for: selectedDate)
            return weekRange?.contains(itemDate) ?? false
        case .month:
            let monthRange = calendar.dateInterval(of: .month, for: selectedDate)
            return monthRange?.contains(itemDate) ?? false
        case .year:
            let yearRange = calendar.dateInterval(of: .year, for: selectedDate)
            return yearRange?.contains(itemDate) ?? false
        }
    }
    
    private func formatTotalFocusTime() -> String {
        let totalMinutes = completedPomodoros * 25
        let hours = totalMinutes / 60
        let minutes = totalMinutes % 60
        return String(format: "time_format".localized, hours, "hours_unit".localized, minutes, "minutes_unit".localized)
    }
    
    private func generatePomodoroTrendData() -> [(Date, Int)] {
        let calendar = Calendar.current
        var result: [(Date, Int)] = []
        
        let endDate = selectedDate
        let startDate: Date
        
        switch selectedTimeFrame {
        case .day:
            startDate = calendar.date(byAdding: .day, value: -6, to: endDate)!
        case .week:
            startDate = calendar.date(byAdding: .weekOfYear, value: -6, to: endDate)!
        case .month:
            startDate = calendar.date(byAdding: .month, value: -6, to: endDate)!
        case .year:
            startDate = calendar.date(byAdding: .year, value: -1, to: endDate)!
        }
        
        var currentDate = startDate
        while currentDate <= endDate {
            let count = filteredItems.filter { item in
                guard item.pomodoroEndTime != nil else { return false }
                return calendar.isDate(item.effectiveTimestamp, inSameDayAs: currentDate) && 
                       item.pomodoroStatus == .completed 
            }.count
            result.append((currentDate, count))
            currentDate = calendar.date(byAdding: .day, value: 1, to: currentDate)!
        }
        
        return result
    }
    
    private var chartsSection: some View {
        VStack(alignment: .leading, spacing: 24) {
            Text("charts_title".localized)
                .font(.headline)
            
            VStack(alignment: .leading, spacing: 16) {
                Text("task_pomodoro_completion".localized)
                    .font(.subheadline)
                ProgressChart(tasks: completionRate, pomodoros: pomodoroCompletionRate)
            }
            
            VStack(alignment: .leading, spacing: 16) {
                Text("pomodoro_trend".localized)
                    .font(.subheadline)
                PomodoroTrendChart(data: generatePomodoroTrendData())
            }
            
            VStack(alignment: .leading, spacing: 16) {
                Text("income_expense_situation".localized)
                    .font(.subheadline)
                IncomeExpenseChart(income: totalIncome, expense: totalExpenses)
            }
        }
    }
}

// 辅助视图组件

struct SectionHeader: View {
    let title: String
    let number: Int
    
    var body: some View {
        HStack {
            Text("\(number). \(title)")
                .font(.headline)
            Spacer()
        }
    }
}

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                Text(title)
            }
            .font(.caption)
            .foregroundColor(.secondary)
            
            Text(value)
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
        }
        .frame(maxWidth: .infinity, alignment: .leading)
        .padding()
        .background(color.opacity(0.1))
        .cornerRadius(10)
    }
}

struct ProgressChart: View {
    let tasks: Double
    let pomodoros: Double
    
    var body: some View {
        Chart {
            BarMark(
                x: .value("category_chart_label".localized, "task_chart_label".localized),
                y: .value("completion_rate_title".localized, tasks)
            )
            .foregroundStyle(Color.blue)
            .annotation(position: .top) {
                Text(tasks, format: .percent.precision(.fractionLength(0)))
                    .font(.caption)
                    .foregroundColor(.blue)
            }
            
            BarMark(
                x: .value("category_chart_label".localized, "pomodoro_chart_label".localized),
                y: .value("completion_rate_title".localized, pomodoros)
            )
            .foregroundStyle(Color.orange)
            .annotation(position: .top) {
                Text(pomodoros, format: .percent.precision(.fractionLength(0)))
                    .font(.caption)
                    .foregroundColor(.orange)
            }
        }
        .frame(height: 100)
        .chartYScale(domain: 0...1)
        .chartXAxis {
            AxisMarks(values: .automatic) { _ in
                AxisValueLabel()
            }
        }
        .chartYAxis {
            AxisMarks(position: .leading, values: .automatic) { value in
                AxisGridLine()
                AxisTick()
                AxisValueLabel() { 
                    Text(value.as(Double.self)!, format: .percent.precision(.fractionLength(0)))
                }
            }
        }
    }
}

struct RecentTasksList: View {
    let tasks: [ChatItem]
    let onTap: (ChatItem) -> Void
    
    var body: some View {
        ForEach(tasks) { task in
            HStack {
                Image(systemName: task.completed ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(task.completed ? .green : .gray)
                Text(task.text)
                    .strikethrough(task.completed)
            }
            .contentShape(Rectangle())
            .onTapGesture {
                onTap(task)
            }
        }
    }
}

struct PomodoroTrendChart: View {
    let data: [(Date, Int)]
    
    var body: some View {
        Chart(data, id: \.0) { item in
            LineMark(
                x: .value("date_chart_label".localized, item.0),
                y: .value("completed_count_label".localized, item.1)
            )
        }
        .frame(height: 150)
    }
}

struct IncomeExpenseChart: View {
    let income: Double
    let expense: Double
    
    var body: some View {
        Chart {
            BarMark(
                x: .value("category_chart_label".localized, "income_chart_label".localized),
                y: .value("amount_chart_label".localized, income)
            )
            .foregroundStyle(.red)
            
            BarMark(
                x: .value("category_chart_label".localized, "expense_chart_label".localized),
                y: .value("amount_chart_label".localized, expense)
            )
            .foregroundStyle(.green)
        }
        .frame(height: 150)
    }
}

struct RecentTransactionsList: View {
    let transactions: [ChatItem]
    
    var body: some View {
        ForEach(transactions) { item in
            HStack {
                Text(item.text)
                Spacer()
                Text(String(format: "%@%.2f", "currency_symbol".localized, item.amount ?? 0))
                    .foregroundColor(item.amount ?? 0 >= 0 ? .red : .green)
            }
        }
    }
}

struct RecentNotesList: View {
    let notes: [ChatItem]
    
    var body: some View {
        ForEach(notes) { note in
            Text(note.text)
                .lineLimit(1)
                .foregroundColor(.secondary)
        }
    }
}

struct EmptyStateView: View {
    var body: some View {
        VStack {
            
            Image(systemName: "chart.bar.xaxis")
                .font(.system(size: 50))
                .foregroundColor(.secondary)
            Text("no_stats_data_available".localized)
                .font(.title2)
                .foregroundColor(.secondary)
            
        }
    }
}

struct DetailView: View {
    let item: ChatItem
    
    var body: some View {
        NavigationView {
            VStack(alignment: .leading, spacing: 16) {
                Text(item.text)
                    .font(.headline)
                
                if item.type == .task {
                    Text(String(format: "status_format".localized, item.completed ? "completed".localized : "incomplete".localized))
                }
                
                if let amount = item.amount {
                    Text(String(format: "amount_display".localized, amount))
                        .foregroundColor(amount >= 0 ? .green : .red)
                }
                
                Text(String(format: "creation_time_display".localized, itemFormatter.string(from: item.effectiveTimestamp)))
                
                Spacer()
            }
            .padding()
            .navigationTitle("details".localized)
            .navigationBarTitleDisplayMode(.inline)
        }
    }
    
    private let itemFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        return formatter
    }()
}

