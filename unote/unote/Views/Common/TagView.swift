import SwiftUI

// 添加新的TagManager类来统一管理标签
struct TagFrequency: Codable, Identifiable {
    let tag: String
    var count: Int
    
    var id: String { tag } // 使其符合 Identifiable 协议
}

class TagManager: ObservableObject {
    @Published var recentTags: [String] = []
    @Published var frequentTags: [TagFrequency] = []
    private let maxRecentTags = 10
    private let userDefaults = UserDefaults.standard
    
    init() {
        loadTags()
    }
    
    func loadTags() {
        recentTags = userDefaults.stringArray(forKey: "recentTags") ?? []
        if let data = userDefaults.data(forKey: "frequentTags"),
           let decoded = try? JSONDecoder().decode([TagFrequency].self, from: data) {
            frequentTags = decoded
        }
    }
    
    func addTag(_ tag: String) {
        // 更新最近使用标签
        if let index = recentTags.firstIndex(of: tag) {
            recentTags.remove(at: index)
        }
        recentTags.insert(tag, at: 0)
        if recentTags.count > maxRecentTags {
            recentTags.removeLast()
        }
        
        // 更新频率统计
        if let index = frequentTags.firstIndex(where: { $0.tag == tag }) {
            frequentTags[index].count += 1
        } else {
            frequentTags.append(TagFrequency(tag: tag, count: 1))
        }
        frequentTags.sort { $0.count > $1.count }
        
        saveTags()
    }
    
    internal func saveTags() {
        userDefaults.set(recentTags, forKey: "recentTags")
        if let encoded = try? JSONEncoder().encode(frequentTags) {
            userDefaults.set(encoded, forKey: "frequentTags")
        }
    }
}

struct TagsView: View {
    @Binding var tags: [String]
    @StateObject private var tagManager = TagManager()
    @State private var tagInput = ""
    @State private var showTagSuggestions = false
    @FocusState private var isInputFocused: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 已选标签展示 - 只在这里显示一次
            if !tags.isEmpty {
                FlowLayout(spacing: 8) {
                    ForEach(tags, id: \.self) { tag in
                        TagView(tag: tag, isSelected: true) {
                            withAnimation {
                                tags.removeAll { $0 == tag }
                            }
                        }
                    }
                }
            }
            
            // 标签输入区域
            HStack {
                Image(systemName: "tag")
                    .foregroundColor(.gray)
                TextField("enter_tags".localized, text: $tagInput)
                    .focused($isInputFocused)
                    .textFieldStyle(PlainTextFieldStyle())
                    .onChange(of: tagInput) { oldValue, newValue in
                        withAnimation {
                            showTagSuggestions = !newValue.isEmpty
                        }
                    }
                    .onSubmit {
                        addTag(tagInput)
                    }
                
                if !tagInput.isEmpty {
                    Button(action: { addTag(tagInput) }) {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(.blue)
                    }
                }
            }
            .padding(8)
            .background(Color(.systemGray6))
            .cornerRadius(8)
            
            // 标签建议区域 - 只显示未选择的标签
            if showTagSuggestions {
                VStack(alignment: .leading, spacing: 8) {
                    if !filteredFrequentTags.isEmpty {
                        Text("common_tags_section".localized)
                            .font(.caption)
                            .foregroundColor(.gray)
                        
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(filteredFrequentTags) { tagInfo in
                                    TagSuggestionView(tag: tagInfo.tag) {
                                        addTag(tagInfo.tag)
                                    }
                                }
                            }
                        }
                    }
                    
                    if !filteredRecentTags.isEmpty {
                        Text("recent_tags_section".localized)
                            .font(.caption)
                            .foregroundColor(.gray)
                        
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(filteredRecentTags, id: \.self) { tag in
                                    TagSuggestionView(tag: tag) {
                                        addTag(tag)
                                    }
                                }
                            }
                        }
                    }
                }
                .padding(.vertical, 8)
            }
        }
        .padding(12)
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }
    
    // 添加过滤已选标签的计算属性
    private var filteredFrequentTags: [TagFrequency] {
        tagManager.frequentTags.filter { !tags.contains($0.tag) }
    }
    
    private var filteredRecentTags: [String] {
        tagManager.recentTags.filter { !tags.contains($0) }
    }
    
    private func addTag(_ tag: String) {
        let trimmedTag = tag.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTag.isEmpty else { return }
        
        withAnimation {
            if !tags.contains(trimmedTag) {
                tags.append(trimmedTag)
                tagManager.addTag(trimmedTag)
            }
            tagInput = ""
            showTagSuggestions = false
            isInputFocused = false
        }
    }
}

// 新增标签建议视图组件
struct TagSuggestionView: View {
    let tag: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(tag)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(Color.blue.opacity(0.1))
                .foregroundColor(.blue)
                .cornerRadius(16)
        }
    }
}

struct TagView: View {
    let tag: String
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            Text(tag)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(isSelected ? Color.blue : Color(.systemGray5))
                .foregroundColor(isSelected ? .white : .primary)
                .cornerRadius(16)
        }
    }
}

struct FlowLayout: Layout {
    var spacing: CGFloat = 8
    
    func sizeThatFits(proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) -> CGSize {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        return layout(sizes: sizes, proposal: proposal).0 // 只返回 CGSize
    }
    
    func placeSubviews(in bounds: CGRect, proposal: ProposedViewSize, subviews: Subviews, cache: inout ()) {
        let sizes = subviews.map { $0.sizeThatFits(.unspecified) }
        let (_, offsets) = layout(sizes: sizes, proposal: proposal)
        
        for (offset, subview) in zip(offsets, subviews) {
            subview.place(at: CGPoint(x: bounds.minX + offset.x, y: bounds.minY + offset.y), proposal: .unspecified)
        }
    }
    
    private func layout(sizes: [CGSize], proposal: ProposedViewSize) -> (CGSize, [CGPoint]) {
        guard let width = proposal.width else { return (.zero, []) }
        
        var offsets: [CGPoint] = []
        var currentX: CGFloat = 0
        var currentY: CGFloat = 0
        var lineHeight: CGFloat = 0
        var maxWidth: CGFloat = 0
        
        for size in sizes {
            if currentX + size.width > width {
                currentX = 0
                currentY += lineHeight + spacing
                lineHeight = 0
            }
            
            offsets.append(CGPoint(x: currentX, y: currentY))
            currentX += size.width + spacing
            lineHeight = max(lineHeight, size.height)
            maxWidth = max(maxWidth, currentX)
        }
        
        return (CGSize(width: maxWidth, height: currentY + lineHeight), offsets)
    }
}
