import SwiftUI

struct TopBarView: View {
    @Binding var selectedFilter: ItemFilter
    @Binding var selectedDate: Date
    @State private var showingDatePicker = false // 新增:控制日期选择器的显示
    @Binding var isShowingStats: Bool
    @Binding var showCalendarTimeline: Bool // 新增：绑定时间线视图状态
    let projectName: String
    @EnvironmentObject var pomodoroManager: PomodoroManager
    let project: Project?
    @Binding var isViewingAllData: Bool // 新增：绑定全部数据视图状态
    @Binding var selectedItemType: ItemType? // 新增:绑定选中的项目类型
    @Binding var selectedTags: Set<String> // 改为 Set 支持多选
    let chatItems: [ChatItem] // 新增：接收聊天项目数组
    @Binding var viewMode: ViewMode // 新增
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showSubscription = false // 添加状态变量控制订阅页面显示
    
    // 新增：获取当前项目下所有唯一标签
    private var availableTags: [String] {
        // 使用 Set 来去重
        let allTags = Set(chatItems.flatMap { $0.tags })
        return Array(allTags).sorted()
    }
    
    // 添加当前显示的内容日期
    @State private var currentContentDate: Date = Date()
    
    private var formattedDate: String {
        if isViewingAllData {
            // 如果是查看全部模式，显示当前内容的日期
            let formatter = DateFormatter()
            formatter.dateFormat = "month_day_format_localized".localized
            return formatter.string(from: currentContentDate)
        } else {
            // 如果是按日查看模式，显示选中的日期
            let formatter = DateFormatter()
            formatter.dateFormat = "month_day_format_localized".localized
            return formatter.string(from: selectedDate)
        }
    }
    
    var body: some View {
        VStack(spacing: 0) {
            HStack(spacing: 12) {
                Button(action: { 
                    // 如果是标准视图或者是订阅用户，允许选择日期
                    if viewMode == .normal || subscriptionManager.isSubscribed {
                        showingDatePicker.toggle()
                    } else {
                        // 其他视图模式下，非订阅用户显示订阅页面
                        showSubscription = true
                    }
                }) {
                    HStack {
                        Image(systemName: "calendar")
                            .font(.system(size: 18))
                        Text(formattedDate)
                            .font(.caption)
                    }
                    .foregroundColor(viewMode == .normal || subscriptionManager.isSubscribed ? .primary : .secondary)
                }

        
                Spacer()
                
                if let activePomodoro = pomodoroManager.activePomodoro {
                    Button(action: { 
                        // 检查是否需要跨项目导航
                        if let pomodoroProject = activePomodoro.project {
                            // 如果番茄钟项目与当前项目不同，先导航到对应项目
                            if pomodoroProject.id != project?.id {
                                // 使用 NotificationCenter 发送导航请求
                                NotificationCenter.default.post(
                                    name: NSNotification.Name("NavigateToProject"),
                                    object: nil,
                                    userInfo: ["project": pomodoroProject]
                                )
                            }
                        } else if project != nil {
                            // 如果番茄钟在未分类项目，但当前在某个项目中
                            NotificationCenter.default.post(
                                name: NSNotification.Name("NavigateToUncategorized"),
                                object: nil
                            )
                        }
                        pomodoroManager.scrollToActivePomodoroItem()
                    }) {
                        HStack {
                            ZStack {
                                Circle()
                                    .stroke(lineWidth: 3)
                                    .opacity(0.3)
                                    .foregroundColor(.gray)
                                
                                Circle()
                                    .trim(from: 0.0, to: pomodoroManager.progress)
                                    .stroke(style: StrokeStyle(lineWidth: 3, lineCap: .round, lineJoin: .round))
                                    .foregroundColor(.blue)
                                    .rotationEffect(Angle(degrees: 270.0))
                                    .animation(.linear, value: pomodoroManager.progress)
                            }
                            .frame(width: 14, height: 14)
                            
                            HStack() {

                                // Text(formatTime(pomodoroManager.remainingTime))
                                //     .font(.caption)
                                    
                                Text(activePomodoro.text)
                                    .font(.caption) 
                                    .lineLimit(1)
                                
                            }
                        }
                        .foregroundColor(.primary)
                    }
                }

                Spacer()



                // 修改视图切换按钮
                Menu {
                    Button(action: {
                        withAnimation(.spring()) {
                            viewMode = .normal
                        }
                    }) {
                        Label("standard_view".localized, systemImage: "message")
                            .if(viewMode == .normal) { view in
                                view.foregroundColor(.blue)
                            }
                            
                    }
                    
                    Button(action: {
                        withAnimation(.spring()) {
                            viewMode = .list
                        }
                    }) {
                        Label("list_view".localized, systemImage: "list.bullet")
                            .if(viewMode == .list) { view in
                                view.foregroundColor(.blue)
                            }
                    }
                    
                    Button(action: {
                        withAnimation(.spring()) {
                            viewMode = .timeline
                        }
                    }) {
                        Label("timeline_view".localized, systemImage: "clock")
                            .if(viewMode == .timeline) { view in
                                view.foregroundColor(.blue)
                            }
                    }
                    
                    Button(action: {
                        withAnimation(.spring()) {
                            viewMode = .diary
                        }
                    }) {
                        Label("diary_view".localized, systemImage: "book")
                            .if(viewMode == .diary) { view in
                                view.foregroundColor(.blue)
                            }
                    }
                } label: {
                    Image(systemName: viewMode.icon)
                        .font(.system(size: viewMode == .normal ? 17 : 18))
                        .foregroundColor(.primary)
                }

                
                // 修改：只保留项目类型筛选按钮，移标签筛选按钮
                Menu {
                    Button(action: {
                        selectedItemType = nil
                    }) {
                        Label("all_types".localized, systemImage: "line.3.horizontal.decrease.circle")
                            .if(selectedItemType == nil) { view in
                                view.foregroundColor(.blue)
                            }
                    }
                    
                    Button(action: {
                        selectedItemType = .note
                    }) {
                        Label("note_filter".localized, systemImage: "note.text")
                            .if(selectedItemType == .note) { view in
                                view.foregroundColor(.blue)
                            }
                    }
                    
                    Button(action: {
                        selectedItemType = .task
                    }) {
                        Label("task_filter".localized, systemImage: "checklist")
                            .if(selectedItemType == .task) { view in
                                view.foregroundColor(.blue)
                            }
                    }
                    
                    Button(action: {
                        selectedItemType = .expense
                    }) {
                        Label("expense_filter".localized, systemImage: "creditcard")
                            .if(selectedItemType == .expense) { view in
                                view.foregroundColor(.blue)
                            }
                    }
                } label: {
                    Image(systemName: itemTypeIcon)
                        .font(.system(size: 18))
                        .foregroundColor(.primary)
                }



                
                
                Button(action: {
                    isShowingStats = true
                }) {
                    Image(systemName: "chart.pie")
                        .font(.system(size: 18))
                        .foregroundColor(.primary)
                }
                

            }
            .padding(.horizontal)
            .frame(height: 44)
            
            if showingDatePicker {
                DatePicker("select_date".localized, selection: $selectedDate, displayedComponents: [.date])
                    .datePickerStyle(GraphicalDatePickerStyle())
                    .padding()
                    .onChange(of: selectedDate) { oldValue, newValue in
                        isViewingAllData = false // 切换到按日查看
                        showingDatePicker = false // 选择后关闭日期选择器
                    }
            }

            Divider()
        }
        .sheet(isPresented: $showSubscription) {
            SubscriptionView()
        }
        // 添加通知监听器来更新当前内容日期
        .onReceive(NotificationCenter.default.publisher(for: NSNotification.Name("UpdateContentDate"))) { notification in
            if let date = notification.object as? Date {
                currentContentDate = date
            }
        }
    }
    
    private func formatTime(_ seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
    
    // 新增：根据选中的项目类型返回对应的图标
    private var itemTypeIcon: String {
        switch selectedItemType {
        case .note:
            return "note.text"
        case .task:
            return "checklist"
        case .expense:
            return "creditcard"
        case nil:
            return "line.3.horizontal.decrease.circle"
        }
    }
}

// 新增：扩展 ChatItem 数组来支持标签过滤
extension Array where Element == ChatItem {
    func filtered(byTag tag: String?) -> [ChatItem] {
        guard let tag = tag else { return self }
        return self.filter { $0.tags.contains(tag) }
    }
}

// 添加 View 扩展来支持条件修饰符
extension View {
    @ViewBuilder func `if`<Content: View>(_ condition: Bool, transform: (Self) -> Content) -> some View {
        if condition {
            transform(self)
        } else {
            self
        }
    }
}

// 新增视图模式枚举
enum ViewMode {
    case normal
    case list
    case timeline
    case diary // 新增日记模式
    
    var icon: String {
        switch self {
        case .normal:
            return "message"
        case .list:
            return "list.bullet"
        case .timeline:
            return "clock"
        case .diary:
            return "book" // 使用文本对齐图标表示日记模式
        }
    }
}
