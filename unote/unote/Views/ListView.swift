import SwiftUI

struct ListView: View {
    let items: [ChatItem]
    let onDelete: (ChatItem) -> Void
    @State private var editingItem: ChatItem?
    @Environment(\.modelContext) private var modelContext
    @EnvironmentObject var pomodoroManager: PomodoroManager
    @StateObject private var subscriptionManager = SubscriptionManager.shared
    @State private var showSubscription = false
    
    // 添加过滤逻辑
    private var filteredItems: [ChatItem] {
        if subscriptionManager.isSubscribed {
            return items
        } else {
            // 获取最新的两天的记录
            let sortedDates = items.map { $0.effectiveTimestamp }
                .map { Calendar.current.startOfDay(for: $0) }
                .unique()
                .sorted(by: >)
                .prefix(2)
            
            let latestTwoDays = Set(sortedDates)
            return items.filter { item in
                let itemDay = Calendar.current.startOfDay(for: item.effectiveTimestamp)
                return latestTwoDays.contains(itemDay)
            }
        }
    }
    
    var body: some View {
        ListContent(
            items: filteredItems, // 使用过滤后的数据
            editingItem: $editingItem,
            onDelete: onDelete,
            onToggleComplete: toggleComplete,
            pomodoroManager: pomodoroManager
        )
        .overlay(
            Group {
                if !subscriptionManager.isSubscribed && !items.isEmpty {
                    VStack {
                        Spacer()
                        HStack(spacing: 16) {
                            VStack(alignment: .leading, spacing: 6) {
                                Text("upgrade_to_premium".localized)
                                    .font(.headline)
                                    .foregroundColor(.primary)
                                Text("unlock_history".localized)
                                    .font(.subheadline)
                                    .foregroundColor(.secondary)
                            }
                            
                            Spacer()
                            
                            Image(systemName: "wand.and.sparkles.inverse")
                                .font(.title2)
                                .foregroundStyle(.primary)
                        }
                        .padding(.horizontal, 20)
                        .padding(.vertical, 16)
                        .background(
                            RoundedRectangle(cornerRadius: 16)
                                .fill(Color(uiColor: .systemBackground))
                                .shadow(color: .black.opacity(0.05), radius: 10, x: 0, y: 2)
                        )
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(Color.primary.opacity(0.1), lineWidth: 1)
                        )
                        .padding(.horizontal, 16)
                        .padding(.bottom, 16)
                        .onTapGesture {
                            showSubscription = true
                        }
                    }
                }
            }
        )
        .sheet(item: $editingItem) { item in
            EditChatItemView(
                item: item,
                onSave: { updatedItem in
                    try? modelContext.save()
                    editingItem = nil
                },
                onDelete: {
                    onDelete(item)
                    editingItem = nil
                }
            )
        }
        .sheet(isPresented: $showSubscription) {
            SubscriptionView()
        }
    }
    
    private func toggleComplete(_ item: ChatItem) {
        item.completed.toggle()
        try? modelContext.save()
    }
}

// 将列表内容移到单独的视图中
private struct ListContent: View {
    let items: [ChatItem]
    @Binding var editingItem: ChatItem?
    let onDelete: (ChatItem) -> Void
    let onToggleComplete: (ChatItem) -> Void
    let pomodoroManager: PomodoroManager
    
    // 定义时间段
    private let timeSlots = [
        (0..<6, "dawn_period".localized),
        (6..<9, "morning_period".localized),
                    (9..<12, "morning".localized),
        (12..<14, "noon_period".localized),
                    (14..<18, "afternoon".localized),
        (18..<22, "evening_period".localized),
        (22..<24, "late_night_period".localized)
    ]
    
    // 修改分组逻辑
    private var groupedItems: [Date: [(String, [ChatItem])]] {
        let byDay = Dictionary(grouping: items) { item in
            Calendar.current.startOfDay(for: item.effectiveTimestamp)
        }
        
        return byDay.mapValues { dayItems in
            // 按时间段分组
            var timeGroupedItems: [String: [ChatItem]] = [:]
            
            for item in dayItems {
                let hour = Calendar.current.component(.hour, from: item.effectiveTimestamp)
                if let timeSlot = timeSlots.first(where: { $0.0.contains(hour) }) {
                    timeGroupedItems[timeSlot.1, default: []].append(item)
                }
            }
            
            // 将每个时间段内的项目按时间排序
            return timeSlots.compactMap { slot -> (String, [ChatItem])? in
                guard let items = timeGroupedItems[slot.1], !items.isEmpty else { return nil }
                return (slot.1, items.sorted(by: { $0.effectiveTimestamp > $1.effectiveTimestamp }))
            }
        }
    }
    
    var body: some View {
        List {
            ForEach(groupedItems.keys.sorted(by: >), id: \.self) { day in
                Section {
                    if let timeGroups = groupedItems[day] {
                        ForEach(timeGroups, id: \.0) { timeSlot, items in
                            Text(timeSlot)
                                .font(.caption)
                                .foregroundColor(.secondary)
                                .listRowSeparator(.hidden)
                                .padding(.top, 8)
                            
                            ForEach(items, id: \.id) { item in
                                ListItemRow(item: item, onToggleComplete: onToggleComplete)
                                    .contentShape(Rectangle())
                                    .onTapGesture {
                                        editingItem = item
                                    }
                                    .contextMenu {
                                        contextMenuButtons(for: item)
                                    }
                                    .swipeActions(edge: .trailing, allowsFullSwipe: true) {
                                        deleteButton(for: item)
                                    }
                                    .swipeActions(edge: .leading, allowsFullSwipe: true) {
                                        if item.type == .task {
                                            completeButton(for: item)
                                        }
                                    }
                            }
                        }
                    }
                } header: {
                    Text(formatDay(day))
                        .font(.title2.bold())
                        .textCase(nil)
                        .foregroundColor(.primary)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.vertical, 12)
                        .padding(.horizontal, 16)
                        .background(Color(uiColor: .systemBackground))
                        .listRowInsets(EdgeInsets())
                }
                .listSectionSpacing(48)
            }
        }
        .listStyle(.plain)
    }
    
    @ViewBuilder
    private func contextMenuButtons(for item: ChatItem) -> some View {
        Button(action: { editingItem = item }) {
                                    Label("edit".localized, systemImage: "pencil")
        }
        
        if item.type == .task {
            Button(action: { onToggleComplete(item) }) {
                Label(
                    item.completed ? "mark_incomplete".localized : "mark_completed".localized,
                    systemImage: item.completed ? "xmark.circle" : "checkmark.circle"
                )
            }
        }
        
        if item.type == .task && !item.completed {
            if pomodoroManager.activePomodoro?.id == item.id {
                Button(action: { pomodoroManager.stopPomodoro() }) {
                    Label("stop_pomodoro".localized, systemImage: "stop.circle")
                }
            } else {
                Button(action: { pomodoroManager.startPomodoro(for: item) }) {
                    Label("start_pomodoro".localized, systemImage: "timer")
                }
            }
        }
        
        Button(role: .destructive, action: { onDelete(item) }) {
            Label("delete".localized, systemImage: "trash")
        }
    }
    
    @ViewBuilder
    private func deleteButton(for item: ChatItem) -> some View {
        Button(role: .destructive) {
            onDelete(item)
        } label: {
            Label("delete".localized, systemImage: "trash")
        }
    }
    
    @ViewBuilder
    private func completeButton(for item: ChatItem) -> some View {
        Button {
            onToggleComplete(item)
        } label: {
            Label(
                item.completed ? "mark_incomplete".localized : "mark_completed".localized,
                systemImage: item.completed ? "xmark.circle" : "checkmark.circle"
            )
        }
        .tint(item.completed ? .gray : .green)
    }
    
    private func formatDay(_ date: Date) -> String {
        if Calendar.current.isDateInToday(date) {
            return "today".localized
        } else if Calendar.current.isDateInYesterday(date) {
            return "yesterday".localized
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "month_day_format_localized".localized
            return formatter.string(from: date)
        }
    }
}

// 将行项目视图保持不变
struct ListItemRow: View {
    let item: ChatItem
    let onToggleComplete: (ChatItem) -> Void
    @EnvironmentObject var pomodoroManager: PomodoroManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(alignment: .center, spacing: 4) {
                // 类型图标 - 保持在背景色之外
                if item.type == .task {
                    Button(action: { onToggleComplete(item) }) {
                        Image(systemName: typeIcon)
                            .foregroundColor(typeColor)
                            .font(.system(size: 16))
                    }
                } else {
                    Image(systemName: typeIcon)
                        .opacity(0.2)
                        .font(.system(size: 16))
                }
                
                // 主要内容 - 应用背景色
                VStack(alignment: .leading, spacing: 4) {
                    HStack(spacing: 2) {
                        Text(item.text)
                            .lineLimit(1)
                            .font(.system(size: 16))
                            .strikethrough(item.type == .task && item.completed)
                            .foregroundColor(item.type == .task && item.completed ? .gray : .primary)
                        
                        if let amount = item.amount {
                            Text("\(amount >= 0 ? "+ " : "- ")¥\(String(format: "%.2f", abs(amount)))")
                                .font(.system(size: 14))
                                .fontWeight(.bold)
                                .foregroundColor(amount >= 0 ? .red : .green)
                                .padding(.horizontal, 4)
                                .padding(.vertical, 2)
                                .background(amount >= 0 ? .red.opacity(0.1) : .green.opacity(0.15))
                                .cornerRadius(4)
                        }
                        
                        if item.type == .task && pomodoroManager.activePomodoro?.id == item.id {
                            Text(timeString(from: pomodoroManager.remainingTime))
                                .font(.caption2)
                                .foregroundColor(.secondary)
                        }
                    }
                    
                    // 添加图片预览
                    if !item.imageData.isEmpty {
                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: 8) {
                                ForEach(item.imageData.indices, id: \.self) { index in
                                    if let uiImage = UIImage(data: item.imageData[index]) {
                                        Image(uiImage: uiImage)
                                            .resizable()
                                            .scaledToFill()
                                            .frame(width: 40, height: 40)
                                            .clipShape(RoundedRectangle(cornerRadius: 6))
                                    }
                                }
                            }
                        }
                        .frame(height: 40)
                    }
                }
                .padding(.vertical, 4)
                .padding(.horizontal, 8)
                
                // 如果有标签显示标签
                if !item.tags.isEmpty {
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack {
                            ForEach(item.tags, id: \.self) { tag in
                                Text("#\(tag)")
                                    .font(.system(size: 12))
                                    .foregroundColor(.blue)
                            }
                        }
                        
                    }
                }
                
                Spacer()
                
                // 时间
                Text(formatTime(item.effectiveTimestamp))
                    .font(.system(size: 12))
                    .foregroundColor(.secondary)
            }
            
            
            
            // 如果有计划时间，显示计划时间
            if let plannedDate = item.plannedDate {
                HStack {
                    Image(systemName: "calendar")
                        .font(.system(size: 12))
                    Text(formatPlannedDate(plannedDate))
                        .font(.system(size: 12))
                }
                .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 4)
        // .padding(.horizontal, 4)
        .background(
            ZStack(alignment: .leading) {
                // 只在活动番茄钟状态下显示背景和进度条
                if item.type == .task && pomodoroManager.activePomodoro?.id == item.id {
                    RoundedRectangle(cornerRadius: 6)
                        .fill(Color.green.opacity(0.15))
                    
                    Rectangle()
                        .fill(Color.green.opacity(0.3))
                        .frame(maxWidth: .infinity)
                        .scaleEffect(x: pomodoroManager.progress, y: 1, anchor: .leading)
                        .animation(.linear(duration: 0.3), value: pomodoroManager.progress)
                }
            }
        )
        .clipShape(RoundedRectangle(cornerRadius: 6))
    }
    
    private var typeIcon: String {
        switch item.type {
        case .note:
            return "note.text"
        case .task:
            return item.completed ? "checkmark.circle.fill" : "circle"
        case .expense:
            return "dollarsign.circle"
        }
    }
    
    private var typeColor: Color {
        switch item.type {
        case .note:
            return .blue
        case .task:
            return item.completed ? .green : .gray
        case .expense:
            return .orange
        }
    }
    
    private func formatTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        return formatter.string(from: item.effectiveTimestamp)
    }
    
    private func formatPlannedDate(_ date: Date) -> String {
        let formatter = DateFormatter()
                    formatter.dateFormat = "month_day_time_format".localized
        return formatter.string(from: date)
    }
    
    private func timeString(from seconds: Int) -> String {
        let minutes = seconds / 60
        let remainingSeconds = seconds % 60
        return String(format: "%02d:%02d", minutes, remainingSeconds)
    }
} 
