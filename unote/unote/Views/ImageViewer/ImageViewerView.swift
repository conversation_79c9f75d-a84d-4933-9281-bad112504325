import SwiftUI
import Photos

public struct ImageViewerView: View {
    let chatItem: ChatItem
    let initialIndex: Int
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    
    // 视图状态
    @State private var selectedIndex: Int
    @State private var showingActionSheet = false
    @State private var showingShareSheet = false
    @State private var showingDeleteAlert = false
    
    // 添加状态变量控制 UI 显示
    @State private var showUI = true
    
    init(chatItem: ChatItem, initialIndex: Int = 0) {
        self.chatItem = chatItem
        self.initialIndex = initialIndex
        self._selectedIndex = State(initialValue: initialIndex)
    }
    
    public var body: some View {
        ZStack {
            // 背景
            Color.black.ignoresSafeArea()
            
            // 图片画廊
            TabView(selection: $selectedIndex) {
                ForEach(Array(chatItem.imageData.enumerated()), id: \.offset) { index, imageData in
                    if let image = UIImage(data: imageData) {
                        PhotoView(
                            image: image,
                            showingActionSheet: $showingActionSheet,
                            showUI: $showUI
                        )
                    }
                }
            }
            .tabViewStyle(.page(indexDisplayMode: showUI ? .always : .never))
            
            // 顶部导航栏
            if showUI {
                VStack {
                    HStack {
                        Button(action: { dismiss() }) {
                            Image(systemName: "chevron.left")
                                .foregroundColor(.white)
                                .font(.system(size: 18, weight: .semibold))
                                .frame(width: 44, height: 44)
                        }
                        
                        Spacer()
                        
                        Text("\(selectedIndex + 1)/\(chatItem.imageData.count)")
                            .foregroundColor(.white)
                            .font(.system(size: 17))
                        
                        Spacer()
                        
                        Button(action: { showingActionSheet = true }) {
                            Image(systemName: "ellipsis")
                                .foregroundColor(.white)
                                .font(.system(size: 18, weight: .semibold))
                                .frame(width: 44, height: 44)
                        }
                    }
                    .padding(.horizontal)
                    .background(
                        LinearGradient(
                            colors: [.black.opacity(0.6), .clear],
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    
                    Spacer()
                }
                .transition(.move(edge: .top).combined(with: .opacity))
            }
        }
        .navigationBarHidden(true)
        .statusBar(hidden: true)
        // 长按菜单
        .confirmationDialog("image_actions".localized, isPresented: $showingActionSheet) {
            Button("share".localized) { showingShareSheet = true }
            Button("save_to_album".localized) { saveToPhotos() }
                            Button("delete".localized, role: .destructive) { showingDeleteAlert = true }
            Button("cancel".localized, role: .cancel) { }
        }
        // 分享菜单
        .sheet(isPresented: $showingShareSheet) {
            if let imageData = chatItem.imageData[safe: selectedIndex] {
                ShareSheet(activityItems: [imageData])
            }
        }
        // 删除确认
        .alert(isPresented: $showingDeleteAlert) {
            Alert(
                            title: Text("confirm_delete_image".localized),
            message: Text("delete_image_message".localized),
                primaryButton: .destructive(Text("delete".localized)) {
                    deleteImage()
                },
                secondaryButton: .cancel()
            )
        }
    }
    
    private func deleteImage() {
        guard selectedIndex < chatItem.imageData.count else { return }
        
        withAnimation {
            chatItem.imageData.remove(at: selectedIndex)
            try? modelContext.save()
            
            if chatItem.imageData.isEmpty {
                dismiss()
            } else {
                selectedIndex = max(0, min(selectedIndex, chatItem.imageData.count - 1))
            }
        }
    }
    
    private func saveToPhotos() {
        guard let imageData = chatItem.imageData[safe: selectedIndex],
              let image = UIImage(data: imageData) else { return }
        
        PHPhotoLibrary.requestAuthorization { status in
            guard status == .authorized else { return }
            
            PHPhotoLibrary.shared().performChanges({
                PHAssetChangeRequest.creationRequestForAsset(from: image)
            }) { success, error in
                if let error = error {
                    print(String(format: "save_image_failed".localized, error.localizedDescription))
                }
            }
        }
    }
}

private struct PhotoView: View {
    let image: UIImage
    @Binding var showingActionSheet: Bool
    @Binding var showUI: Bool
    
    // 添加图片尺寸计算
    private var imageSize: CGSize {
        let aspectRatio = image.size.width / image.size.height
        return CGSize(width: UIScreen.main.bounds.width, height: UIScreen.main.bounds.width / aspectRatio)
    }
    
    @State private var scale: CGFloat = 1.0
    @State private var offset: CGSize = .zero
    @GestureState private var dragOffset: CGSize = .zero
    
    @State private var scrollDirection: Axis.Set?  // 添加状态来跟踪当前滚动方向
    @GestureState private var isDragging = false  // 添加状态来跟踪是否正在拖动
    
    private var scrollDirections: Axis.Set {
        if scale > 1 {
            return [.horizontal, .vertical]
        }
        // 如果正在拖动，使用已确定的方向
        if isDragging, let direction = scrollDirection {
            return direction
        }
        // 默认方向
        return imageSize.height > UIScreen.main.bounds.height ? .vertical : .horizontal
    }
    
    var body: some View {
        GeometryReader { geometry in
            ScrollView(scrollDirections, showsIndicators: false) {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: imageSize.width, height: imageSize.height)
                    .scaleEffect(scale)
                    .offset(x: offset.width + dragOffset.width,
                           y: offset.height + dragOffset.height)
                    .padding(.vertical, max(0, (geometry.size.height - imageSize.height) / 2))
                    .padding(.horizontal, max(0, (geometry.size.width - imageSize.width) / 2))
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
            .simultaneousGesture(
                DragGesture()
                    .updating($isDragging) { _, state, _ in
                        state = true
                    }
                    .onChanged { value in
                        guard scale <= 1, scrollDirection == nil else { return }
                        
                        // 确定滚动方向
                        let translation = value.translation
                        let absX = abs(translation.width)
                        let absY = abs(translation.height)
                        
                        if absX > 10 || absY > 10 {  // 添加一个小的阈值
                            if absX > absY {
                                scrollDirection = .horizontal
                            } else {
                                scrollDirection = .vertical
                            }
                        }
                    }
                    .onEnded { _ in
                        // 重置滚动方向
                        scrollDirection = nil
                    }
            )
            // 点击手势
            .onTapGesture(count: 2) {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    if scale > 1 {
                        scale = 1
                        offset = .zero
                    } else {
                        scale = 2
                    }
                }
            }
            .onTapGesture(count: 1) {
                withAnimation(.easeInOut(duration: 0.25)) {
                    showUI.toggle()
                }
            }
            .onLongPressGesture {
                showingActionSheet = true
            }
        }
    }
}

struct ShareSheet: UIViewControllerRepresentable {
    let activityItems: [Any]
    
    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
        return controller
    }
    
    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// 安全数组访问扩展
extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}
