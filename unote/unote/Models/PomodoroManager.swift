import Foundation
import SwiftUI
import SwiftData
import UserNotifications
import os
import Combine

@MainActor
class PomodoroManager: ObservableObject {
    static let shared = PomodoroManager()
    
    private let logger = Logger(subsystem: Bundle.main.bundleIdentifier ?? "com.kowazone.huoguang", category: "PomodoroManager")
    @Published var activePomodoro: ChatItem?
    @Published var remainingTime: Int = 25 * 60
    @Published var progress: CGFloat = 0.0
    @Published var isInBreak: Bool = false
    @AppStorage("defaultPomodoroWorkTime") private var defaultPomodoroWorkTime = 25
    @AppStorage("defaultPomodoroBreakTime") private var defaultPomodoroBreakTime = 5
    @Published var pomodoroStatus: PomodoroStatus = .notStarted
    @Published var completedPomodoros: Int = 0
    @Published var totalFocusTime: TimeInterval = 0
    @Published var startTime: Date?
    @Published var pausedRemainingTime: Int?

    private var timer: AnyCancellable?
    private var endTime: Date?

    let scrollToActivePomodoro = PassthroughSubject<Void, Never>()
    
    deinit {
        // 直接取消timer而不调用stopTimer()，因为deinit不能使用@MainActor
        timer?.cancel()
        timer = nil
        logger.info("PomodoroManager 正在销毁，已清理计时器")
    }

    func scrollToActivePomodoroItem() {
        scrollToActivePomodoro.send(())
    }

    func startPomodoro(for item: ChatItem) {
        Task { @MainActor in
            logger.info("开始启动番茄钟")
            
            guard activePomodoro == nil else {
                logger.warning("尝试启动新的番茄钟，但已有活动的番茄钟")
                return
            }
            
            activePomodoro = item
            remainingTime = defaultPomodoroWorkTime * 60
            pomodoroStatus = .running
            item.pomodoroStatus = .running
            item.pomodoroStartTime = Date()
            startTime = Date()
            endTime = startTime?.addingTimeInterval(TimeInterval(remainingTime))
            
            logger.info("番茄钟状态已设置")
            
            startTimer()
            
            logger.info("计时器已启动")
            
            scrollToActivePomodoroItem()
            
            logger.info("准备发送开始通知")
            do {
                try await sendStartNotification()
                logger.info("开始通知已发送成功")
            } catch {
                logger.error("发送开始通知失败: \(error.localizedDescription, privacy: .public)")
            }
            
            logger.info("准备安排结束通知")
            do {
                try await scheduleNotification(for: .work)
                logger.info("结束通知已安排成功")
            } catch {
                logger.error("安排结束通知失败: \(error.localizedDescription, privacy: .public)")
            }
            
            logger.info("成功启动番茄钟")
        }
    }
    
    func stopPomodoro() {
        if let item = activePomodoro {
            if pomodoroStatus == .running && remainingTime == 0 && !isInBreak {
                // 工作阶段完成，但还没开始休息
                item.pomodoroStatus = .completed
                item.pomodoroEndTime = Date()
                item.completedPomodoros += 1
                completedPomodoros += 1
                checkAchievements()
            } else {
                item.pomodoroStatus = .notStarted
            }
        }
        activePomodoro = nil
        remainingTime = defaultPomodoroWorkTime * 60
        pomodoroStatus = .notStarted
        progress = 0.0
        isInBreak = false
        stopTimer()
        
        cancelNotification()
    }
    
    func pausePomodoro() {
        pomodoroStatus = .paused
        pausedRemainingTime = remainingTime
        startTime = nil
        stopTimer()
    }
    
    func resumePomodoro() {
        pomodoroStatus = .running
        startTime = Date().addingTimeInterval(-Double(defaultPomodoroWorkTime * 60 - (pausedRemainingTime ?? remainingTime)))
        pausedRemainingTime = nil
        startTimer()
    }
    
    private func startTimer() {
        stopTimer()
        
        timer = Timer.publish(every: 1, on: .main, in: .common)
            .autoconnect()
            .sink { [weak self] _ in
                self?.updateRemainingTime()
            }
        
        logger.info("计时器已启动")
    }
    
    private func stopTimer() {
        timer?.cancel()
        timer = nil
    }
    
    private func updateRemainingTime() {
        guard let endTime = endTime else { return }
        
        let now = Date()
        let newRemainingTime = max(0, Int(endTime.timeIntervalSince(now)))
        
        if newRemainingTime > 0 {
            if newRemainingTime != self.remainingTime {
                self.remainingTime = newRemainingTime
                self.totalFocusTime = Double(self.defaultPomodoroWorkTime * 60 - newRemainingTime)
                self.progress = 1.0 - CGFloat(newRemainingTime) / CGFloat(self.defaultPomodoroWorkTime * 60)
            }
        } else {
            if self.isInBreak {
                self.stopPomodoro()
            } else {
                self.completeWorkSession()
            }
        }
    }
    
    private func completeWorkSession() {
        if let item = activePomodoro {
            item.pomodoroStatus = .completed
            item.pomodoroEndTime = Date()
            item.completedPomodoros += 1
            completedPomodoros += 1
            checkAchievements()
        }
        startBreak()
    }
    
    private func startBreak() {
        isInBreak = true
        remainingTime = defaultPomodoroBreakTime * 60
        Task {
            do {
                try await scheduleNotification(for: .rest)
            } catch {
                logger.error("安排休息通知失败: \(error.localizedDescription, privacy: .public)")
            }
        }
    }
    
    private func scheduleNotification(for type: PomodoroNotificationType) async throws {
        let content = UNMutableNotificationContent()
        content.title = type == .work ? "专注时间结束" : "休息时间结束"
        content.body = type == .work ? "太棒了！你已经专注工作了25分钟。现在休息一下吧！" : "休息结束了。准备开始新的工作周期吗？"
        content.sound = .default
        
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: TimeInterval(remainingTime), repeats: false)
        let request = UNNotificationRequest(identifier: UUID().uuidString, content: content, trigger: trigger)
        
        do {
            try await UNUserNotificationCenter.current().add(request)
            logger.info("通知已安排: \(type == .work ? "工作结束" : "休息结束", privacy: .public)")
        } catch {
            logger.error("安排通知失败: \(error.localizedDescription, privacy: .public)")
            throw error
        }
    }
    
    private func cancelNotification() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
    }
    
    private func sendStartNotification() async throws {
        let content = UNMutableNotificationContent()
        content.title = "番茄钟开始了！"
        content.body = "集中精力工作 \(defaultPomodoroWorkTime) 分钟，然后休息 \(defaultPomodoroBreakTime) 分钟。"
        content.sound = .default

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 2, repeats: false)
        let request = UNNotificationRequest(identifier: "PomodoroStartNotification", content: content, trigger: trigger)

        do {
            try await UNUserNotificationCenter.current().add(request)
            logger.info("开始通知已发送")
        } catch {
            logger.error("发送开始通知失败: \(error.localizedDescription, privacy: .public)")
            throw error
        }
    }
    
    private func checkAchievements() {
        // 实现成就系统，例如连续完成5个番茄钟
    }
}

enum PomodoroNotificationType {
    case work
    case rest
}
