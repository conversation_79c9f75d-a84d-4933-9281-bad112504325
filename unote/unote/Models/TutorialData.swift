import Foundation
import SwiftData
import Swift<PERSON>

@MainActor
class TutorialData {
    static func createTutorialProject(modelContext: ModelContext) {
        // 检查是否已存在教程项目
        let descriptor = FetchDescriptor<Project>(
            predicate: #Predicate<Project> { project in
                project.name == "tutorial_data_project_name".localized
            }
        )
        
        guard (try? modelContext.fetch(descriptor))?.isEmpty ?? true else {
            return // 如果已存在教程项目，直接返回
        }
        
        let now = Date()
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: now)
        
        // 创建教程项目
        let tutorialProject = Project(name: "tutorial_data_project_name".localized, avatarType: .icon, avatarName: "📖")
        modelContext.insert(tutorialProject)
        
        // 创建日记项目
        let diaryProject = Project(name: "diary_project_name".localized, avatarType: .icon, avatarName: "📔")
        modelContext.insert(diaryProject)
        
        // 创建日记欢迎内容
        let diaryItems: [ChatItem] = [
            {
                let item = ChatItem(text: "diary_welcome_title".localized, type: .note)
                item.comments = [
                    Comment(text: "diary_welcome_comment_1".localized),
                    Comment(text: "diary_welcome_comment_2".localized),
                    Comment(text: "diary_welcome_comment_3".localized)
                ]
                return item
            }(),
            
            ChatItem(text: "diary_suggestions_title".localized, type: .note),
            ChatItem(text: "diary_suggestion_1".localized, type: .note),
            ChatItem(text: "diary_suggestion_2".localized, type: .note),
            ChatItem(text: "diary_suggestion_3".localized, type: .note),
            ChatItem(text: "diary_suggestion_4".localized, type: .note)
        ]
        
        // 设置日记内容的时间戳
        let setDiaryTimestamps = { (items: [ChatItem]) in
            let start = calendar.date(bySettingHour: 8, minute: 0, second: 0, of: today)!
            for (index, item) in items.enumerated() {
                item.timestamp = calendar.date(byAdding: .minute, value: index, to: start) ?? now
                item.project = diaryProject
            }
        }
        
        setDiaryTimestamps(diaryItems)
        
        // 早晨 9:00-10:00 基础功能
        let morningItems: [ChatItem] = [
            ChatItem(text: "👋 欢迎使用 UNote!", type: .note),
            {
                let item = ChatItem(text: "像聊天一样记录生活", type: .note)
                item.comments = [
                    Comment(text: "记录应该是轻松自然的"),
                    Comment(text: "让效率源于习惯"),
                    Comment(text: "简单是最好的设计")
                ]
                return item
            }(),
            
            ChatItem(text: "输入框：直接输入文字并发送", type: .note),
            ChatItem(text: "支持表情：输入时直接选择", type: .note),
            ChatItem(text: "任务模式：点击左侧图标切换", type: .task),
            ChatItem(text: "添加图片：点击相机图标", type: .note),
        ]
        
        // 上午 10:00-11:00 记账功能
        let accountingItems: [ChatItem] = [
            {
                let item = ChatItem(text: "tutorial_data_accounting_title".localized, type: .note)
                item.comments = [
                    Comment(text: "tutorial_data_accounting_comment_1".localized),
                    Comment(text: "tutorial_data_accounting_comment_2".localized),
                    Comment(text: "tutorial_data_accounting_comment_3".localized)
                ]
                return item
            }(),
            
            ChatItem(text: "demo_breakfast".localized, amount: -15.0, type: .expense, category: "🍳 " + "demo_category_dining".localized),
            ChatItem(text: "公交车", amount: -2.0, type: .expense, category: "🚌 交通"),
            ChatItem(text: "工资", amount: 5000.0, type: .expense, category: "💼 " + "salary_category".localized),
            ChatItem(text: "手机充值", amount: -50.0, type: .expense, category: "📱 通讯"),
            ChatItem(text: "超市购物", amount: -108.5, type: .expense, category: "🛒 " + "shopping_category".localized),
        ]
        
        // 上午 10:00-11:00 快捷操作
        let basicItems: [ChatItem] = [
            {
                let item = ChatItem(text: "tutorial_data_shortcuts_title".localized, type: .note)
                item.comments = [
                    Comment(text: "tutorial_data_shortcuts_comment_1".localized),
                    Comment(text: "tutorial_data_shortcuts_comment_2".localized),
                    Comment(text: "tutorial_data_shortcuts_comment_3".localized)
                ]
                return item
            }(),
            
            ChatItem(text: "tutorial_data_shortcuts_1".localized, type: .note),
            ChatItem(text: "tutorial_data_shortcuts_2".localized, type: .note),
            ChatItem(text: "tutorial_data_shortcuts_3".localized, type: .note),
        ]
        
        // 中午 12:00-13:00 内容管理
        let contentItems: [ChatItem] = [
            {
                let item = ChatItem(text: "tutorial_data_content_org_title".localized, type: .note)
                item.comments = [
                    Comment(text: "tutorial_data_content_org_comment_1".localized),
                    Comment(text: "tutorial_data_content_org_comment_2".localized),
                    Comment(text: "tutorial_data_content_org_comment_3".localized)
                ]
                return item
            }(),
            
            ChatItem(text: "tutorial_data_content_org_1".localized, type: .note, tags: ["demo_category_example".localized]),
            ChatItem(text: "tutorial_data_content_org_2".localized, type: .note),
            ChatItem(text: "tutorial_data_content_org_3".localized, type: .note),
            ChatItem(text: "tutorial_data_content_org_4".localized, type: .note),
        ]

        // 下午 13:00-14:00 内容筛选
        let _: [ChatItem] = [
            {
                let item = ChatItem(text: "灵活的筛选功能，点击顶部工具栏筛选图标切换", type: .note)
                item.comments = [
                    Comment(text: "快速找到需要的内容"),
                    Comment(text: "按类型分类查看"),
                    Comment(text: "让信息更有条理")
                ]
                return item
            }(),
            
            ChatItem(text: "📝 笔记筛选：点击笔记标签", type: .note),
            ChatItem(text: "✅ 任务筛选：点击任务标签", type: .note),
            ChatItem(text: "💰 记账筛选：点击记账标签", type: .note),
            ChatItem(text: "🔍 全部内容：点击全部标签", type: .note),
        ]
        
        // 下午 14:00-15:00 视图切换
        let viewItems: [ChatItem] = [
            {
                let item = ChatItem(text: "👀 浏览方式", type: .note)
                item.comments = [
                    Comment(text: "不同视角看待内容"),
                    Comment(text: "适应不同使用场景"),
                    Comment(text: "让信息更有价值")
                ]
                return item
            }(),
            ChatItem(text: "聊天视图：点击聊天图标", type: .note),
            ChatItem(text: "清单视图：点击清单图标", type: .note),
            ChatItem(text: "日记视图：点击日记图标", type: .note),
            ChatItem(text: "时间线视图：点击日历图标", type: .note),
            ChatItem(text: "视图切换：顶部工具栏切换", type: .note),
        ]
        
        // 傍晚 16:00-17:00 番茄钟功能
        let advancedItems: [ChatItem] = [
            {
                let item = ChatItem(text: "专注和效率", type: .note)
                item.comments = [
                    Comment(text: "让工具更好地服务生活"),
                    Comment(text: "提升时间利用效率"),
                    Comment(text: "培养良好的习惯")
                ]
                return item
            }(),
            
            {
                let item = ChatItem(
                    text: "🍅 番茄钟：长按任务-启动番茄钟",
                    type: .task,
                    pomodoroCount: 1
                )
                return item
            }(),
            
            ChatItem(text: "番茄钟操作：长按任务类型内容，点击启动番茄钟", type: .task),
            ChatItem(text: "全屏模式：启动番茄钟后，点击气泡内的全屏图标", type: .note),
            ChatItem(text: "专注时长：25分钟工作+5分钟休息", type: .note),
            ChatItem(text: "计划任务：长按任务设置番茄钟数量", type: .note),
            ChatItem(text: "番茄钟提醒：工作/休息时间到会有提示", type: .note),
            
        ]
        
        // 傍晚 17:00-18:00 数据统计
        let _: [ChatItem] = [
            {
                let item = ChatItem(text: "数据分析", type: .note)
                item.comments = [
                    Comment(text: "了解自己的使用情况"),
                    Comment(text: "提高时间管理效率"),
                    Comment(text: "培养良好的习惯")
                ]
                return item
            }(),
            
            ChatItem(text: "📊 数据统计：点击顶部统计卡片", type: .note),
            ChatItem(text: "📈 趋势分析：查看使用趋势", type: .note),
            ChatItem(text: "🔄 数据导出：设置-导出数据", type: .note),
        ]
        
        // 晚上 18:00-19:00 使用建议
        let tipsItems: [ChatItem] = [
            {
                let item = ChatItem(text: "✨ 使用建议", type: .note)
                item.comments = [
                    Comment(text: "记录创造价值"),
                    Comment(text: "让生活更有序"),
                    Comment(text: "期待你的故事")
                ]
                return item
            }(),
            
            ChatItem(text: "记录灵感：随时输入记录", type: .note),
            ChatItem(text: "整理归类：使用标签和项目", type: .note),
            ChatItem(text: "培养习惯：定期回顾和整理", type: .note),
            ChatItem(text: "记录美好：让生活更有序", type: .note),
        ]
        
        // 晚上 19:00-20:00 项目管理
        let projectItems: [ChatItem] = [
            {
                let item = ChatItem(text: "📁 项目管理", type: .note)
                item.comments = [
                    Comment(text: "分类整理你的内容"),
                    Comment(text: "让记录更有条理"),
                    Comment(text: "适应不同场景需求")
                ]
                return item
            }(),
            
            ChatItem(text: "创建项目：点击主页左上角加号图标-新建项目", type: .note),
            ChatItem(text: "编辑项目：长按项目-编辑", type: .note),
            ChatItem(text: "项目分类示例：", type: .note),
            ChatItem(text: "学习笔记", type: .note),
            ChatItem(text: "工作事项", type: .note),
            ChatItem(text: "生活记录", type: .note),
            ChatItem(text: "财务管理", type: .note),
            ChatItem(text: "个人成长", type: .note),
        ]
        
        // 晚上 20:00-21:00 寄语
        let endingItems: [ChatItem] = [
            {
                let item = ChatItem(text: "💌 寄语", type: .note)
                item.comments = [
                    Comment(text: "感谢使用 UNote"),
                    Comment(text: "愿它能成为你的得力助手"),
                    Comment(text: "让我们一起记录生活的美好")
                ]
                return item
            }(),
            ChatItem(text: "请从顶部开始查看教程", type: .note),
            ChatItem(text: "每一个记录都是生活的印记", type: .note),
            ChatItem(text: "每一天都值得被温柔对待", type: .note),
        ]
        
        // 修改时间间隔设置
        let setTimestamps = { (items: [ChatItem], startHour: Int) in
            let start = calendar.date(bySettingHour: startHour, minute: 0, second: 0, of: today)!
            for (index, item) in items.enumerated() {
                item.timestamp = calendar.date(byAdding: .minute, value: index, to: start) ?? now
                item.project = tutorialProject
            }
        }
        
        // 设置每个时间段的起始时间
        setTimestamps(morningItems, 9)      // 9:00 开始
        setTimestamps(accountingItems, 10)   // 10:00 开始
        setTimestamps(basicItems, 11)       // 11:00 开始
        setTimestamps(contentItems, 12)     // 12:00 开始
        setTimestamps(viewItems, 14)        // 14:00 开始
        setTimestamps(advancedItems, 16)    // 16:00 开始
        setTimestamps(tipsItems, 18)        // 18:00 开始
        setTimestamps(projectItems, 19)     // 19:00 开始
        setTimestamps(endingItems, 20)    // 20:00 开始
        
        // 合并并插入所有内容
        let allItems = morningItems + accountingItems + basicItems + contentItems + 
                      viewItems + advancedItems + tipsItems + projectItems +
                      endingItems
        
        // 插入所有教程内容
        allItems.forEach { modelContext.insert($0) }
    }
} 
