import Foundation
import SwiftData

class TutorialManager {
    static let shared = TutorialManager()
    
    private let tutorialProjectKey = "tutorialProjectCreated"
    
    func createTutorialProjectIfNeeded(context: ModelContext) {
        if UserDefaults.standard.bool(forKey: tutorialProjectKey) {
            return
        }
        
        let project = Project(name: "tutorial_project_name".localized, avatarType: .icon, avatarName: "sparkles")
        
        // 按照用户使用流程创建教程内容
        let tutorials: [ChatItem] = [
            // 1. 欢迎和基础输入
            createWelcomeMessage(),
            createBasicInputTutorial(),
            createTypesSwitchTutorial(),
            
            // 2. 内容编辑和组织
            createTagsTutorial1(),
            createTagsTutorial2(),
            createImageTutorial(),
            createCommentTutorial(),
            
            // 3. 任务管理
            createTaskTutorial1(),
            createTaskTutorial2(),
            createPomodoroTutorial1(),
            createPomodoroTutorial2(),
            
            // 4. 记账功能
            createExpenseTutorial1(),
            createExpenseTutorial2(),
            
            // 5. 查看和分析
            createViewModeTutorial1(),
            createViewModeTutorial2(),
            createStatsTutorial(),
            
            // 6. 项目管理
            createProjectTutorial1(),
            createProjectTutorial2()
        ]
        
        project.chatItems = tutorials
        context.insert(project)
        try? context.save()
        
        UserDefaults.standard.set(true, forKey: tutorialProjectKey)
    }
    
    // 1. 欢迎和基础输入
    private func createWelcomeMessage() -> ChatItem {
        ChatItem(text: "tutorial_welcome_message".localized, type: .note)
    }
    
    private func createBasicInputTutorial() -> ChatItem {
        ChatItem(text: "tutorial_basic_input".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    private func createTypesSwitchTutorial() -> ChatItem {
        ChatItem(text: "tutorial_types_switch".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    // 2. 内容编辑和组织
    private func createTagsTutorial1() -> ChatItem {
        ChatItem(text: "tutorial_tags_1".localized, type: .note, tags: ["tutorial_tag".localized, "tags".localized])
    }
    
    private func createTagsTutorial2() -> ChatItem {
        ChatItem(text: "tutorial_tags_2".localized, type: .note, tags: ["tutorial_tag".localized, "tags".localized])
    }
    
    private func createImageTutorial() -> ChatItem {
        ChatItem(text: "tutorial_image".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    private func createCommentTutorial() -> ChatItem {
        ChatItem(text: "tutorial_comment".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    // 3. 任务管理
    private func createTaskTutorial1() -> ChatItem {
        ChatItem(text: "tutorial_task_1".localized, type: .task, tags: ["tutorial_tag".localized])
    }
    
    private func createTaskTutorial2() -> ChatItem {
        ChatItem(text: "tutorial_task_2".localized, type: .task, tags: ["tutorial_tag".localized])
    }
    
    private func createPomodoroTutorial1() -> ChatItem {
        ChatItem(text: "tutorial_pomodoro_1".localized, type: .task, tags: ["tutorial_tag".localized], pomodoroCount: 1)
    }
    
    private func createPomodoroTutorial2() -> ChatItem {
        ChatItem(text: "tutorial_pomodoro_2".localized, type: .task, tags: ["tutorial_tag".localized], pomodoroCount: 1)
    }
    
    // 4. 记账功能
    private func createExpenseTutorial1() -> ChatItem {
        let item = ChatItem(text: "tutorial_expense_1".localized, amount: -9.9, type: .expense, tags: ["tutorial_tag".localized])
        item.category = "other_category".localized
        return item
    }
    
    private func createExpenseTutorial2() -> ChatItem {
        ChatItem(text: "tutorial_expense_2".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    // 5. 查看和分析
    private func createViewModeTutorial1() -> ChatItem {
        ChatItem(text: "tutorial_view_mode_1".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    private func createViewModeTutorial2() -> ChatItem {
        ChatItem(text: "tutorial_view_mode_2".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    private func createStatsTutorial() -> ChatItem {
        ChatItem(text: "tutorial_stats".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    // 6. 项目管理
    private func createProjectTutorial1() -> ChatItem {
        ChatItem(text: "tutorial_project_1".localized, type: .note, tags: ["tutorial_tag".localized])
    }
    
    private func createProjectTutorial2() -> ChatItem {
        ChatItem(text: "tutorial_project_2".localized, type: .note, tags: ["tutorial_tag".localized])
    }
} 