enum SubscriptionTier {
    case free
    case premium
    
    // 项目限制
    var projectLimit: Int {
        switch self {
        case .free: return 5  // 免费版最多3个项目分类
        case .premium: return .max  // 高级版无限制
        }
    }
    
    // 每个项目的任务数量限制
    var tasksPerProjectLimit: Int {
        switch self {
        case .free: return 100  // 免费版每个项目最多100个任务
        case .premium: return .max  // 高级版无限制
        }
    }
    
    // 图片附件限制
    var imageAttachmentsLimit: Int {
        switch self {
        case .free: return 3  // 免费版每个任务最多3张图片
        case .premium: return 10  // 高级版每个任务最多10张图片
        }
    }
    
    // 标签限制
    var tagsLimit: Int {
        switch self {
        case .free: return 5  // 免费版最多5个标签
        case .premium: return .max  // 高级版无限制
        }
    }
}

struct PremiumFeatures {
    // 基础功能(免费版和高级版都有)
    static let basicFeatures = [
        "创建和管理任务",
        "基础番茄钟功能",
        "简单的数据统计",
        "基础提醒功能",
        "深色模式支持",
        "最多3个项目分类",
        "基础标签功能(5个)",
        "简单的任务筛选"
    ]
    
    // 高级功能(仅高级版)
    static let premiumFeatures = [
        // 项目管理
        "无限项目分类",
        "项目模板功能",
        "项目归档功能",
        "项目协作分享",
        
        // 数据分析
        "详细的数据统计图表",
        "自定义统计时间范围",
        "数据导出(CSV/PDF)",
        "进度趋势分析",
        "完成率分析",
        "时间利用率报告",
        
        // 番茄钟增强
        "自定义番茄钟时长",
        "番茄钟统计报告",
        "自动智能休息提醒",
        "番茄钟白噪音",
        
        // 提醒增强
        "重复任务规则",
        "位置提醒",
        "智能提醒建议",
        "日历同步",
        
        // 标签管理
        "无限标签创建",
        "标签组管理",
        "智能标签建议",
        
        // 数据备份
        "iCloud同步",
        "自动备份",
        "历史版本恢复",
        
        // 界面定制
        "自定义主题色彩",
        "自定义字体大小",
        "自定义视图布局",
        
        // 其他高级功能
        "批量编辑",
        "高级搜索过滤",
        "子任务管理",
        "笔记附件(图片/文件)",
        "任务优先级管理"
    ]
}
