import Foundation
import SwiftData

class DemoData {
    static func createDemoProjects(modelContext: ModelContext) {
        let now = Date()
        let calendar = Calendar.current
        let today = calendar.startOfDay(for: now)
        
        // 创建项目
        let workProject = Project(name: "demo_project_work".localized, avatarType: .icon, avatarName: "💼")
        let lifeProject = Project(name: "demo_project_life".localized, avatarType: .icon, avatarName: "🏠")
        let healthProject = Project(name: "demo_project_health".localized, avatarType: .icon, avatarName: "💪")
        
        [workProject, lifeProject, healthProject].forEach { modelContext.insert($0) }
        
        var allItems: [ChatItem] = []
        
        // 早晨 6:30-8:00
        let morningItems: [ChatItem] = [
            {
                let item = ChatItem(text: "demo_morning_run".localized,
                    type: .task,
                    pomodoroCount: 1,
                    project: healthProject)
                item.priority = .high
                return item
            }(),
            
            ChatItem(text: "demo_run_record".localized, 
                    type: .note,
                    project: healthProject),
                    
            ChatItem(text: "demo_run_pace".localized, 
                    type: .note,
                    project: healthProject),
                    
            ChatItem(text: "demo_run_idea".localized, 
                    type: .note,
                    project: healthProject),
                    
            ChatItem(text: "demo_breakfast".localized,
                    amount: 12.0,
                    type: .expense,
                    project: lifeProject,
                    category: "demo_category_dining".localized),
                    
            ChatItem(text: "demo_breakfast_comment".localized, 
                    type: .note,
                    project: lifeProject)
        ]
        
        // 上午工作 9:00-12:00
        let morningWorkItems: [ChatItem] = [
            ChatItem(text: "demo_new_day".localized, 
                    type: .note,
                    project: workProject),
                    
            {
                let item = ChatItem(text: "demo_design_review".localized,
                    type: .task,
                    pomodoroCount: 2,
                    project: workProject)
                item.priority = .high
                return item
            }(),
            
            ChatItem(text: "demo_user_feedback".localized, 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "demo_design_idea".localized, 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "demo_interaction_idea".localized, 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "demo_sort_question".localized, 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "demo_team_discuss".localized, 
                    type: .note,
                    project: workProject)
        ]
        
        // 中午休息 12:00-14:00
        let noonItems: [ChatItem] = [
            ChatItem(text: "午餐",
                    amount: 28.0,
                    type: .expense,
                    project: lifeProject,
                    category: "餐饮"),
                    
            ChatItem(text: "这家店的红烧肉不错 😋", 
                    type: .note,
                    project: lifeProject),
                    
            {
                let item = ChatItem(text: "午休时间 😴",
                    type: .task,
                    pomodoroCount: 1,
                    project: lifeProject)
                item.priority = .medium
                return item
            }(),
            
            ChatItem(text: "睡前看到一篇好文章，待会记得读完 📚", 
                    type: .note,
                    project: lifeProject)
        ]
        
        // 下午工作 14:00-18:00
        let afternoonWorkItems: [ChatItem] = [
            {
                let item = ChatItem(text: "修改设计方案 ✏️",
                    type: .task,
                    pomodoroCount: 3,
                    project: workProject)
                item.priority = .high
                return item
            }(),
            
            ChatItem(text: "突然想到：要不要加个深色模式？🌙", 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "首页可以加个天气小组件 ☀️", 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "支持自定义主题色会更好 🎨", 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "记得研究一下动画效果 ✨", 
                    type: .note,
                    project: workProject),
                    
            ChatItem(text: "文具",
                    amount: 89.9,
                    type: .expense,
                    project: workProject,
                    category: "办公"),
                    
            ChatItem(text: "这支笔写字很舒服，推荐给同事 ✍️", 
                    type: .note,
                    project: workProject)
        ]
        
        // 傍晚生活 18:00-22:00
        let eveningItems: [ChatItem] = [
            {
                let item = ChatItem(text: "去超市买东西 🛒",
                    type: .task,
                    project: lifeProject)
                item.priority = .medium
                return item
            }(),
            
            ChatItem(text: "水果",
                    amount: 58.5,
                    type: .expense,
                    project: lifeProject,
                    category: "日用"),
                    
            ChatItem(text: "这个季节的草莓特别甜 🍓", 
                    type: .note,
                    project: lifeProject),
                    
            ChatItem(text: "蔬菜",
                    amount: 32.0,
                    type: .expense,
                    project: lifeProject,
                    category: "日用"),
                    
            ChatItem(text: "今天的西蓝花看起来很新鲜 🥦", 
                    type: .note,
                    project: lifeProject),
                    
            ChatItem(text: "牛奶面包",
                    amount: 38.0,
                    type: .expense,
                    project: lifeProject,
                    category: "日用"),
                    
            ChatItem(text: "这个面包店的全麦面包不错 🍞", 
                    type: .note,
                    project: lifeProject),
                    
            {
                let item = ChatItem(text: "练习瑜伽 🧘‍♀️",
                    type: .task,
                    pomodoroCount: 1,
                    project: healthProject)
                item.priority = .medium
                return item
            }(),
            
            ChatItem(text: "找到一个不错的瑜伽视频，收藏了 📺", 
                    type: .note,
                    project: healthProject),
                    
            ChatItem(text: "晚餐",
                    amount: 45.0,
                    type: .expense,
                    project: lifeProject,
                    category: "餐饮"),
                    
            ChatItem(text: "尝试了新菜谱，味道不错 👨‍🍳", 
                    type: .note,
                    project: lifeProject),
                    
            ChatItem(text: "记得明天买些姜和蒜 🧄", 
                    type: .note,
                    project: lifeProject),
                    
            ChatItem(text: "今天完成了所有计划，感觉很充实 ✨", 
                    type: .note,
                    project: lifeProject)
        ]
        
        // 设置时间间隔
        let setTimestamps = { (items: [ChatItem], start: Date) in
            for (index, item) in items.enumerated() {
                item.timestamp = calendar.date(byAdding: .minute, value: index * 10, to: start) ?? now
            }
        }
        
        // 设置每个时间段的起始时间
        let morningStart = calendar.date(bySettingHour: 6, minute: 30, second: 0, of: today)!
        let workStart = calendar.date(bySettingHour: 9, minute: 0, second: 0, of: today)!
        let noonStart = calendar.date(bySettingHour: 12, minute: 0, second: 0, of: today)!
        let afternoonStart = calendar.date(bySettingHour: 14, minute: 0, second: 0, of: today)!
        let eveningStart = calendar.date(bySettingHour: 18, minute: 0, second: 0, of: today)!
        
        // 设置时间戳
        setTimestamps(morningItems, morningStart)
        setTimestamps(morningWorkItems, workStart)
        setTimestamps(noonItems, noonStart)
        setTimestamps(afternoonWorkItems, afternoonStart)
        setTimestamps(eveningItems, eveningStart)
        
        // 合并并插入所有数据
        allItems.append(contentsOf: morningItems)
        allItems.append(contentsOf: morningWorkItems)
        allItems.append(contentsOf: noonItems)
        allItems.append(contentsOf: afternoonWorkItems)
        allItems.append(contentsOf: eveningItems)
        
        allItems.forEach { modelContext.insert($0) }
    }
} 