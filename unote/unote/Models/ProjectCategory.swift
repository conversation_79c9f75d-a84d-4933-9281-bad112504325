enum ProjectCategory: String, CaseIterable {
    // 工作场景
    case work = "工作"
    case project = "项目"
    case meeting = "会议"
    case client = "客户"
    case report = "报告"
    
    // 学习场景
    case study = "学习"
    case course = "课程"
    case research = "研究"
    case notes = "笔记"
    case exam = "考试"
    
    // 个人场景
    case personal = "个人"
    case diary = "日记"
    case plan = "计划"
    case finance = "理财"
    case health = "健康"
    
    // 创作场景
    case creative = "创作"
    case writing = "写作"
    case design = "设计"
    case video = "视频"
    case music = "音乐"
    
    // 其他
    case other = "其他"
    
    var icon: String {
        switch self {
        case .work: return "💼"
        case .project: return "📊"
        case .meeting: return "👥"
        case .client: return "🤝"
        case .report: return "📑"
            
        case .study: return "📚"
        case .course: return "🎓"
        case .research: return "🔬"
        case .notes: return "📝"
        case .exam: return "✍️"
            
        case .personal: return "👤"
        case .diary: return "📔"
        case .plan: return "📅"
        case .finance: return "💰"
        case .health: return "❤️"
            
        case .creative: return "🎨"
        case .writing: return "✏️"
        case .design: return "🎯"
        case .video: return "🎬"
        case .music: return "🎵"
            
        case .other: return "📁"
        }
    }
    
    // 场景预设
    static let scenes: [String: [ProjectCategory]] = [
        "工作": [.work, .project, .meeting, .client, .report],
        "学习": [.study, .course, .research, .notes, .exam],
        "个人": [.personal, .diary, .plan, .finance, .health],
        "创作": [.creative, .writing, .design, .video, .music],
        "其他": [.other]
    ]
} 