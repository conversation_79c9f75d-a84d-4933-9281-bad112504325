import SwiftUI
import SwiftData
import Combine

// MARK: - 统一应用状态管理器
@MainActor
final class AppState: ObservableObject {
    static let shared = AppState()
    
    // MARK: - Core State
    @Published var isLoading = false
    @Published var errorMessage: String?
    @Published var successMessage: String?
    
    // MARK: - UI State  
    @Published var selectedTab: AppTab = .home
    @Published var isShowingSettings = false
    @Published var isShowingSearch = false
    @Published var selectedProject: Project?
    @Published var selectedChatItem: ChatItem?
    
    // MARK: - Filter State
    @Published var selectedFilter: ContentFilter = .all
    @Published var selectedDate = Date()
    @Published var selectedTags: Set<String> = []
    @Published var searchText = ""
    
    // MARK: - Input State
    @Published var inputText = ""
    @Published var inputAmount = ""
    @Published var selectedType: ItemType = .note
    @Published var pomodoroCount = 0
    @Published var selectedImages: [UIImage] = []
    
    // MARK: - Language Management
    // 语言管理交给 LocalizationManager 统一处理
    
    private var cancellables = Set<AnyCancellable>()
    
    private init() {
        setupSubscriptions()
    }
    
    // MARK: - Setup
    private func setupSubscriptions() {
        // 自动清除错误消息
        $errorMessage
            .compactMap { $0 }
            .delay(for: .seconds(3), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.errorMessage = nil
            }
            .store(in: &cancellables)
        
        // 自动清除成功消息  
        $successMessage
            .compactMap { $0 }
            .delay(for: .seconds(2), scheduler: DispatchQueue.main)
            .sink { [weak self] _ in
                self?.successMessage = nil
            }
            .store(in: &cancellables)
    }
    
    // MARK: - Actions
    func showError(_ message: String) {
        errorMessage = message
    }
    
    func showSuccess(_ message: String) {
        successMessage = message
    }
    
    func clearInput() {
        inputText = ""
        inputAmount = ""
        selectedImages = []
        pomodoroCount = 0
    }
    
    func selectProject(_ project: Project?) {
        selectedProject = project
        selectedFilter = project == nil ? .all : .project
    }
    
    func resetFilters() {
        selectedFilter = .all
        selectedDate = Date()
        selectedTags = []
        searchText = ""
    }
}

// MARK: - App Tabs
enum AppTab: String, CaseIterable {
    case home = "home"
    case search = "search"
    case stats = "stats"
    case settings = "settings"
    
    var title: String {
        switch self {
        case .home: return "主页"
        case .search: return "搜索"
        case .stats: return "统计"
        case .settings: return "设置"
        }
    }
    
    var iconName: String {
        switch self {
        case .home: return "house"
        case .search: return "magnifyingglass"
        case .stats: return "chart.bar"
        case .settings: return "gear"
        }
    }
}

// 注意：ContentFilter 定义在 App/ContentView.swift 中

// MARK: - View Extensions
extension View {
    func withAppState() -> some View {
        self.environmentObject(AppState.shared)
    }
} 