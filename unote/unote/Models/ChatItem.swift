import Foundation
import SwiftData

@Model
final class ChatItem: @unchecked Sendable {
    @Attribute(.unique) var id: UUID
    var text: String
    var amount: Double? // 新增金额字段
    var type: ItemType
    @Attribute private var tagsString: String = ""
    var tags: [String] {
        get { tagsString.isEmpty ? [] : tagsString.components(separatedBy: ",") }
        set { tagsString = newValue.joined(separator: ",") }
    }
    var pomodoroCount: Int?
    var timestamp: Date
    var completed: Bool
    var group: String?
    var dueDate: Date?
    @Relationship(deleteRule: .cascade) var subItems: [ChatItem]?
    @Relationship(deleteRule: .cascade) var imageDataItems: [ImageData]?
    var imageData: [Data] {
        get { imageDataItems?.map { $0.data } ?? [] }
        set { 
            imageDataItems = newValue.map { ImageData(data: $0) }
        }
    }
    var pomodoroStartTime: Date?
    var pomodoroEndTime: Date?
    var pomodoroStatus: PomodoroStatus
    var comments: [Comment]
    var completedPomodoros: Int
    @Relationship var project: Project?
    var order: Int16
    var startTime: Date?
    var endTime: Date?
    var repeatOptionRawValue: String?
    var isAllDay: Bool = false
    var category: String = "支出"
    var priorityRawValue: Int = 0 // 存储原始的Int值
    var plannedDate: Date?
    var plannedTime: Date?
    var isFavorite: Bool = false  // 添加收藏标记
    var customTimestamp: Date? // 新增自定义时间字段
    
    var priority: Priority {
        get {
            return Priority(rawValue: priorityRawValue) ?? .none
        }
        set {
            priorityRawValue = newValue.rawValue
        }
    }
    var url: URL? // URL链接
    var notes: String? // 备注
    var location: String? // 位置
    var isLocationBased: Bool = false // 是否基于位置的提醒
    var reminderTime: Date? // 提醒时间
    var isReminderSet: Bool = false // 是否设置提醒
    
    var repeatOption: RepeatOption? {
        get {
            guard let rawValue = repeatOptionRawValue else { return nil }
            return RepeatOption(rawValue: rawValue)
        }
        set {
            repeatOptionRawValue = newValue?.rawValue
        }
    }
    
    var effectiveTimestamp: Date {
        get { customTimestamp ?? timestamp }
        set { customTimestamp = newValue }
    }
    
    init(text: String, amount: Double? = nil, type: ItemType = .note, tags: [String] = [], pomodoroCount: Int? = nil, completed: Bool = false, group: String? = nil, dueDate: Date? = nil, imageData: [Data] = [], project: Project? = nil, plannedDate: Date? = nil, plannedTime: Date? = nil, repeatOption: RepeatOption? = nil, category: String = "支出", isFavorite: Bool = false, customTimestamp: Date? = nil) {
        self.id = UUID()
        self.text = text
        self.amount = amount
        self.type = amount != nil ? .expense : type // 如果有金额,自动设置为expense类型
        self.tagsString = tags.joined(separator: ",")
        self.pomodoroCount = pomodoroCount
        self.timestamp = Date()
        self.customTimestamp = customTimestamp
        self.completed = completed
        self.group = group
        self.dueDate = dueDate
        self.subItems = []
        self.imageDataItems = imageData.map { ImageData(data: $0) }
        self.pomodoroStatus = .notStarted
        self.completedPomodoros = 0
        self.comments = []
        self.project = project
        self.order = 0
        self.startTime = nil
        self.endTime = nil
        self.repeatOptionRawValue = repeatOption?.rawValue
        self.category = category
        self.plannedDate = plannedDate
        self.plannedTime = plannedTime
        self.isFavorite = isFavorite
    }
    
    func updatePlannedDate(_ newDate: Date?) {
        self.plannedDate = newDate
        // 如果有需要，这里可以添加其他相关字段的更新
    }
}

struct Comment: Codable, Identifiable, Sendable {
    let id: UUID
    var text: String
    let timestamp: Date
    
    init(text: String) {
        self.id = UUID()
        self.text = text
        self.timestamp = Date()
    }
}

enum ItemType: String, Codable, Sendable {
    case note
    case task
    case expense
    
    var description: String {
        switch self {
        case .note: return "note_type".localized
        case .task: return "task_type".localized
        case .expense: return "expense_type".localized
        }
    }
}

// 添加 PomodoroStatus 枚举定义
enum PomodoroStatus: String, Codable, Sendable {
    case notStarted
    case running
    case paused
    case completed
    
    var description: String {
        switch self {
        case .notStarted: return "not_started".localized
        case .running: return "in_progress".localized
        case .paused: return "paused".localized
        case .completed: return "completed".localized
        }
    }
}

enum RepeatOption: String, Codable, CaseIterable, Sendable {
    case never
    case daily
    case weekly
    case monthly
    case yearly

    var description: String {
        switch self {
        case .never: return "never".localized
        case .daily: return "daily".localized
        case .weekly: return "weekly".localized
        case .monthly: return "monthly".localized
        case .yearly: return "yearly".localized
        }
    }
}

enum Priority: Int, CaseIterable, Identifiable {
    case none = 0
    case low = 1
    case medium = 2
    case high = 3
    
    var id: Int { self.rawValue }
    
    var description: String {
        switch self {
        case .none: return "priority_none".localized
        case .low: return "priority_low".localized
        case .medium: return "priority_medium".localized
        case .high: return "priority_high".localized
        }
    }
}

@Model
final class ImageData {
    var data: Data
    
    init(data: Data) {
        self.data = data
    }
}
