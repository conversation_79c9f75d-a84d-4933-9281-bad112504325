import SwiftUI
import StoreKit

// MARK: - 订阅功能模型
struct SubscriptionFeature: Identifiable {
    let id = UUID()
    let title: String
    let description: String
    let icon: String
    
    static let allFeatures = [
        SubscriptionFeature(
            title: "unlimited_projects_title".localized,
            description: "unlimited_projects_desc".localized,
            icon: "folder.badge.plus"
        ),
        SubscriptionFeature(
            title: "all_view_modes_title".localized,
            description: "all_view_modes_desc".localized,
            icon: "calendar"
        ),
        SubscriptionFeature(
            title: "data_export_title".localized,
            description: "data_export_desc".localized,
            icon: "square.and.arrow.up"
        ),
        SubscriptionFeature(
            title: "advanced_stats_title".localized,
            description: "advanced_stats_desc".localized,
            icon: "chart.bar"
        ),
        SubscriptionFeature(
            title: "cloud_sync_title".localized,
            description: "cloud_sync_desc".localized,
            icon: "icloud"
        )
    ]
}

// MARK: - 商店产品枚举
enum StoreProduct: String, CaseIterable {
    case monthly = "sub_pro"
    case yearly = "sub_pro_year"
    case lifetime = "pro_lifetime"
    
    var localizedTitle: String {
        switch self {
        case .monthly: return "monthly_plan".localized
        case .yearly: return "yearly_plan".localized
        case .lifetime: return "lifetime_plan".localized
        }
    }
    
    var localizedDescription: String {
        switch self {
        case .monthly: return "monthly_plan_desc".localized
        case .yearly: return "yearly_plan_desc".localized
        case .lifetime: return "lifetime_plan_desc".localized
        }
    }
}

// MARK: - 订阅产品模型
struct SubscriptionProduct: Identifiable {
    let id: String
    let type: StoreProduct
    let title: String
    let price: String
    let description: String
    let isRecommended: Bool
    let discount: String?
    let product: Product?
    let period: String?
    let features: String
    
    static func from(_ product: Product, type: StoreProduct) -> SubscriptionProduct {
        return SubscriptionProduct(
            id: product.id,
            type: type,
            title: type.localizedTitle,
            price: product.displayPrice,
            description: type.localizedDescription,
            isRecommended: type == .yearly,
            discount: type == .yearly ? "save_40_percent".localized : nil,
            product: product,
            period: type == .lifetime ? nil : (type == .monthly ? "per_month".localized : "per_year".localized),
            features: "all_premium_features".localized
        )
    }
}

// MARK: - 订阅样式定义
struct SubscriptionStyle {
    static let gradient = LinearGradient(
        colors: [.blue.opacity(0.8), .purple.opacity(0.8)],
        startPoint: .topLeading,
        endPoint: .bottomTrailing
    )
    
    static let cardBackground = Color.white.opacity(0.15)
    static let shadowColor = Color.black.opacity(0.1)
    static let highlightColor = Color.yellow
    static let primaryColor = Color(.theme0)
    static let successColor = Color.green
    static let secondaryBackground = Color(.secondarySystemBackground)
}

// MARK: - 背景颜色定义
struct BackgroundColor {
    static let background = Color(UIColor.systemBackground)
    static let secondaryBackground = Color(UIColor.secondarySystemBackground)
}

// MARK: - 订阅产品网格组件
struct SubscriptionProductGrid: View {
    let products: [SubscriptionProduct]
    @Binding var selectedProduct: SubscriptionProduct?
    
    var body: some View {
        VStack(spacing: 12) {
            ForEach(products) { product in
                SubscriptionCardView(
                    product: product,
                    isSelected: selectedProduct?.id == product.id,
                    onSelect: {
                        selectedProduct = product
                    }
                )
            }
        }
        .padding(.horizontal, 20)
    }
}

// MARK: - 已订阅功能列表视图
struct SubscribedFeatureListView: View {
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            ForEach(SubscriptionFeature.allFeatures) { feature in
                HStack(spacing: 12) {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(SubscriptionStyle.successColor)
                        .font(.system(size: 16))
                    
                    VStack(alignment: .leading, spacing: 2) {
                        Text(feature.title)
                            .font(.system(size: 15, weight: .medium))
                        Text(feature.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                }
            }
        }
        .padding(.horizontal, 20)
    }
} 